/**
 * @typedef {Object} ESModuleOptions
 * @property {string | RegExp | Array<string | RegExp>} [exclude] Files to explicitly exclude from flagged as ES Modules.
 * @property {string | RegExp | Array<string | RegExp>} [include] Files to explicitly include for flagged as ES Modules.
 */

/**
 * @typedef {Object} ReactRefreshLoaderOptions
 * @property {boolean} [const] Enables usage of ES6 `const` and `let` in generated runtime code.
 * @property {boolean | ESModuleOptions} [esModule] Enables strict ES Modules compatible runtime.
 */

/**
 * @typedef {import('type-fest').SetRequired<ReactRefreshLoaderOptions, 'const'>} NormalizedLoaderOptions
 */

module.exports = {};
