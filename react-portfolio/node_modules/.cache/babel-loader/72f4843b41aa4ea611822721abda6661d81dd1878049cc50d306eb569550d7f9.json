{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/DarkVeil.js\";\nimport React from 'react';\nimport './DarkVeil.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DarkVeil = ({\n  className = \"\"\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `dark-veil ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"veil-layer veil-layer-1\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"veil-layer veil-layer-2\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"veil-layer veil-layer-3\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"veil-noise\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = DarkVeil;\nexport default DarkVeil;\nvar _c;\n$RefreshReg$(_c, \"DarkVeil\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON>", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/DarkVeil.js"], "sourcesContent": ["import React from 'react';\nimport './DarkVeil.css';\n\nconst DarkVeil = ({ className = \"\" }) => {\n  return (\n    <div className={`dark-veil ${className}`}>\n      <div className=\"veil-layer veil-layer-1\"></div>\n      <div className=\"veil-layer veil-layer-2\"></div>\n      <div className=\"veil-layer veil-layer-3\"></div>\n      <div className=\"veil-noise\"></div>\n    </div>\n  );\n};\n\nexport default DarkVeil;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,QAAQ,GAAGA,CAAC;EAAEC,SAAS,GAAG;AAAG,CAAC,KAAK;EACvC,oBACEF,OAAA;IAAKE,SAAS,EAAE,aAAaA,SAAS,EAAG;IAAAC,QAAA,gBACvCH,OAAA;MAAKE,SAAS,EAAC;IAAyB;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAC/CP,OAAA;MAAKE,SAAS,EAAC;IAAyB;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAC/CP,OAAA;MAAKE,SAAS,EAAC;IAAyB;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAC/CP,OAAA;MAAKE,SAAS,EAAC;IAAY;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC/B,CAAC;AAEV,CAAC;AAACC,EAAA,GATIP,QAAQ;AAWd,eAAeA,QAAQ;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}