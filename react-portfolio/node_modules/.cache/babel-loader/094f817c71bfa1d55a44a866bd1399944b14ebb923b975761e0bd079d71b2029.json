{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/DarkVeil.js\",\n  _s = $RefreshSig$();\nimport React, { useRef, useEffect, useState } from 'react';\nimport './DarkVeil.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DarkVeil = ({\n  className = \"\"\n}) => {\n  _s();\n  const canvasRef = useRef(null);\n  const animationRef = useRef(null);\n  const [dimensions, setDimensions] = useState({\n    width: 0,\n    height: 0\n  });\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n    const ctx = canvas.getContext('2d');\n    if (!ctx) return;\n\n    // Set canvas size\n    const updateSize = () => {\n      const rect = canvas.getBoundingClientRect();\n      const dpr = window.devicePixelRatio || 1;\n      const width = Math.max(rect.width, 1);\n      const height = Math.max(rect.height, 1);\n      canvas.width = width * dpr;\n      canvas.height = height * dpr;\n      ctx.scale(dpr, dpr);\n      canvas.style.width = width + 'px';\n      canvas.style.height = height + 'px';\n      setDimensions({\n        width,\n        height\n      });\n    };\n    updateSize();\n    window.addEventListener('resize', updateSize);\n\n    // Veil particles\n    const particles = [];\n    const particleCount = 150;\n\n    // Initialize particles\n    for (let i = 0; i < particleCount; i++) {\n      particles.push({\n        x: Math.random() * dimensions.width,\n        y: Math.random() * dimensions.height,\n        size: Math.random() * 2 + 0.5,\n        speedX: (Math.random() - 0.5) * 0.5,\n        speedY: (Math.random() - 0.5) * 0.5,\n        opacity: Math.random() * 0.3 + 0.1,\n        life: Math.random() * 100,\n        maxLife: Math.random() * 100 + 50\n      });\n    }\n\n    // Animation loop\n    let time = 0;\n    const animate = () => {\n      time += 0.01;\n\n      // Clear canvas with dark background\n      ctx.fillStyle = 'rgba(0, 0, 0, 0.02)';\n      ctx.fillRect(0, 0, dimensions.width, dimensions.height);\n\n      // Create veil effect with gradients\n      const gradient1 = ctx.createRadialGradient(dimensions.width * 0.3, dimensions.height * 0.3, 0, dimensions.width * 0.3, dimensions.height * 0.3, dimensions.width * 0.8);\n      gradient1.addColorStop(0, 'rgba(5, 8, 12, 0.9)');\n      gradient1.addColorStop(0.3, 'rgba(10, 15, 20, 0.7)');\n      gradient1.addColorStop(0.7, 'rgba(0, 0, 0, 0.95)');\n      gradient1.addColorStop(1, 'transparent');\n      ctx.fillStyle = gradient1;\n      ctx.fillRect(0, 0, dimensions.width, dimensions.height);\n\n      // Second gradient layer\n      const gradient2 = ctx.createRadialGradient(dimensions.width * 0.7 + Math.sin(time) * 50, dimensions.height * 0.7 + Math.cos(time * 0.8) * 30, 0, dimensions.width * 0.7, dimensions.height * 0.7, dimensions.width * 0.6);\n      gradient2.addColorStop(0, 'rgba(8, 12, 18, 0.8)');\n      gradient2.addColorStop(0.4, 'rgba(15, 20, 25, 0.6)');\n      gradient2.addColorStop(0.8, 'rgba(0, 0, 0, 0.9)');\n      gradient2.addColorStop(1, 'transparent');\n      ctx.globalCompositeOperation = 'multiply';\n      ctx.fillStyle = gradient2;\n      ctx.fillRect(0, 0, dimensions.width, dimensions.height);\n      ctx.globalCompositeOperation = 'source-over';\n\n      // Draw and update particles\n      particles.forEach((particle, index) => {\n        // Update particle\n        particle.x += particle.speedX + Math.sin(time + index * 0.1) * 0.1;\n        particle.y += particle.speedY + Math.cos(time + index * 0.1) * 0.1;\n        particle.life++;\n\n        // Wrap around screen\n        if (particle.x < 0) particle.x = dimensions.width;\n        if (particle.x > dimensions.width) particle.x = 0;\n        if (particle.y < 0) particle.y = dimensions.height;\n        if (particle.y > dimensions.height) particle.y = 0;\n\n        // Reset particle if life exceeded\n        if (particle.life > particle.maxLife) {\n          particle.x = Math.random() * dimensions.width;\n          particle.y = Math.random() * dimensions.height;\n          particle.life = 0;\n          particle.opacity = Math.random() * 0.3 + 0.1;\n        }\n\n        // Draw particle\n        const alpha = particle.opacity * (1 - particle.life / particle.maxLife);\n        ctx.fillStyle = `rgba(255, 255, 255, ${alpha * 0.1})`;\n        ctx.beginPath();\n        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);\n        ctx.fill();\n      });\n\n      // Add noise texture\n      const imageData = ctx.getImageData(0, 0, dimensions.width, dimensions.height);\n      const data = imageData.data;\n      for (let i = 0; i < data.length; i += 4) {\n        if (Math.random() < 0.02) {\n          const noise = Math.random() * 10;\n          data[i] += noise; // Red\n          data[i + 1] += noise; // Green\n          data[i + 2] += noise; // Blue\n        }\n      }\n      ctx.putImageData(imageData, 0, 0);\n      animationRef.current = requestAnimationFrame(animate);\n    };\n    animate();\n    return () => {\n      window.removeEventListener('resize', updateSize);\n      if (animationRef.current) {\n        cancelAnimationFrame(animationRef.current);\n      }\n    };\n  }, [dimensions.width, dimensions.height]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `dark-veil-container ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(\"canvas\", {\n      ref: canvasRef,\n      className: \"dark-veil-canvas\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 159,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dark-veil-overlay\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 158,\n    columnNumber: 5\n  }, this);\n};\n_s(DarkVeil, \"wmQfh6sk44RhTBcfaDTp0svxuQ8=\");\n_c = DarkVeil;\nexport default DarkVeil;\nvar _c;\n$RefreshReg$(_c, \"DarkVeil\");", "map": {"version": 3, "names": ["React", "useRef", "useEffect", "useState", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON>", "className", "_s", "canvasRef", "animationRef", "dimensions", "setDimensions", "width", "height", "canvas", "current", "ctx", "getContext", "updateSize", "rect", "getBoundingClientRect", "dpr", "window", "devicePixelRatio", "Math", "max", "scale", "style", "addEventListener", "particles", "particleCount", "i", "push", "x", "random", "y", "size", "speedX", "speedY", "opacity", "life", "maxLife", "time", "animate", "fillStyle", "fillRect", "gradient1", "createRadialGradient", "addColorStop", "gradient2", "sin", "cos", "globalCompositeOperation", "for<PERSON>ach", "particle", "index", "alpha", "beginPath", "arc", "PI", "fill", "imageData", "getImageData", "data", "length", "noise", "putImageData", "requestAnimationFrame", "removeEventListener", "cancelAnimationFrame", "children", "ref", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/DarkVeil.js"], "sourcesContent": ["import React, { useRef, useEffect, useState } from 'react';\nimport './DarkVeil.css';\n\nconst DarkVeil = ({ className = \"\" }) => {\n  const canvasRef = useRef(null);\n  const animationRef = useRef(null);\n  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });\n\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n\n    const ctx = canvas.getContext('2d');\n    if (!ctx) return;\n\n    // Set canvas size\n    const updateSize = () => {\n      const rect = canvas.getBoundingClientRect();\n      const dpr = window.devicePixelRatio || 1;\n\n      const width = Math.max(rect.width, 1);\n      const height = Math.max(rect.height, 1);\n\n      canvas.width = width * dpr;\n      canvas.height = height * dpr;\n\n      ctx.scale(dpr, dpr);\n      canvas.style.width = width + 'px';\n      canvas.style.height = height + 'px';\n\n      setDimensions({ width, height });\n    };\n\n    updateSize();\n    window.addEventListener('resize', updateSize);\n\n    // Veil particles\n    const particles = [];\n    const particleCount = 150;\n\n    // Initialize particles\n    for (let i = 0; i < particleCount; i++) {\n      particles.push({\n        x: Math.random() * dimensions.width,\n        y: Math.random() * dimensions.height,\n        size: Math.random() * 2 + 0.5,\n        speedX: (Math.random() - 0.5) * 0.5,\n        speedY: (Math.random() - 0.5) * 0.5,\n        opacity: Math.random() * 0.3 + 0.1,\n        life: Math.random() * 100,\n        maxLife: Math.random() * 100 + 50\n      });\n    }\n\n    // Animation loop\n    let time = 0;\n    const animate = () => {\n      time += 0.01;\n      \n      // Clear canvas with dark background\n      ctx.fillStyle = 'rgba(0, 0, 0, 0.02)';\n      ctx.fillRect(0, 0, dimensions.width, dimensions.height);\n\n      // Create veil effect with gradients\n      const gradient1 = ctx.createRadialGradient(\n        dimensions.width * 0.3, \n        dimensions.height * 0.3, \n        0,\n        dimensions.width * 0.3, \n        dimensions.height * 0.3, \n        dimensions.width * 0.8\n      );\n      gradient1.addColorStop(0, 'rgba(5, 8, 12, 0.9)');\n      gradient1.addColorStop(0.3, 'rgba(10, 15, 20, 0.7)');\n      gradient1.addColorStop(0.7, 'rgba(0, 0, 0, 0.95)');\n      gradient1.addColorStop(1, 'transparent');\n\n      ctx.fillStyle = gradient1;\n      ctx.fillRect(0, 0, dimensions.width, dimensions.height);\n\n      // Second gradient layer\n      const gradient2 = ctx.createRadialGradient(\n        dimensions.width * 0.7 + Math.sin(time) * 50, \n        dimensions.height * 0.7 + Math.cos(time * 0.8) * 30, \n        0,\n        dimensions.width * 0.7, \n        dimensions.height * 0.7, \n        dimensions.width * 0.6\n      );\n      gradient2.addColorStop(0, 'rgba(8, 12, 18, 0.8)');\n      gradient2.addColorStop(0.4, 'rgba(15, 20, 25, 0.6)');\n      gradient2.addColorStop(0.8, 'rgba(0, 0, 0, 0.9)');\n      gradient2.addColorStop(1, 'transparent');\n\n      ctx.globalCompositeOperation = 'multiply';\n      ctx.fillStyle = gradient2;\n      ctx.fillRect(0, 0, dimensions.width, dimensions.height);\n      ctx.globalCompositeOperation = 'source-over';\n\n      // Draw and update particles\n      particles.forEach((particle, index) => {\n        // Update particle\n        particle.x += particle.speedX + Math.sin(time + index * 0.1) * 0.1;\n        particle.y += particle.speedY + Math.cos(time + index * 0.1) * 0.1;\n        particle.life++;\n\n        // Wrap around screen\n        if (particle.x < 0) particle.x = dimensions.width;\n        if (particle.x > dimensions.width) particle.x = 0;\n        if (particle.y < 0) particle.y = dimensions.height;\n        if (particle.y > dimensions.height) particle.y = 0;\n\n        // Reset particle if life exceeded\n        if (particle.life > particle.maxLife) {\n          particle.x = Math.random() * dimensions.width;\n          particle.y = Math.random() * dimensions.height;\n          particle.life = 0;\n          particle.opacity = Math.random() * 0.3 + 0.1;\n        }\n\n        // Draw particle\n        const alpha = particle.opacity * (1 - particle.life / particle.maxLife);\n        ctx.fillStyle = `rgba(255, 255, 255, ${alpha * 0.1})`;\n        ctx.beginPath();\n        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);\n        ctx.fill();\n      });\n\n      // Add noise texture\n      const imageData = ctx.getImageData(0, 0, dimensions.width, dimensions.height);\n      const data = imageData.data;\n      \n      for (let i = 0; i < data.length; i += 4) {\n        if (Math.random() < 0.02) {\n          const noise = Math.random() * 10;\n          data[i] += noise;     // Red\n          data[i + 1] += noise; // Green\n          data[i + 2] += noise; // Blue\n        }\n      }\n      \n      ctx.putImageData(imageData, 0, 0);\n\n      animationRef.current = requestAnimationFrame(animate);\n    };\n\n    animate();\n\n    return () => {\n      window.removeEventListener('resize', updateSize);\n      if (animationRef.current) {\n        cancelAnimationFrame(animationRef.current);\n      }\n    };\n  }, [dimensions.width, dimensions.height]);\n\n  return (\n    <div className={`dark-veil-container ${className}`}>\n      <canvas\n        ref={canvasRef}\n        className=\"dark-veil-canvas\"\n      />\n      <div className=\"dark-veil-overlay\" />\n    </div>\n  );\n};\n\nexport default DarkVeil;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,OAAO,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,QAAQ,GAAGA,CAAC;EAAEC,SAAS,GAAG;AAAG,CAAC,KAAK;EAAAC,EAAA;EACvC,MAAMC,SAAS,GAAGR,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMS,YAAY,GAAGT,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM,CAACU,UAAU,EAAEC,aAAa,CAAC,GAAGT,QAAQ,CAAC;IAAEU,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE,CAAC,CAAC;EAErEZ,SAAS,CAAC,MAAM;IACd,MAAMa,MAAM,GAAGN,SAAS,CAACO,OAAO;IAChC,IAAI,CAACD,MAAM,EAAE;IAEb,MAAME,GAAG,GAAGF,MAAM,CAACG,UAAU,CAAC,IAAI,CAAC;IACnC,IAAI,CAACD,GAAG,EAAE;;IAEV;IACA,MAAME,UAAU,GAAGA,CAAA,KAAM;MACvB,MAAMC,IAAI,GAAGL,MAAM,CAACM,qBAAqB,CAAC,CAAC;MAC3C,MAAMC,GAAG,GAAGC,MAAM,CAACC,gBAAgB,IAAI,CAAC;MAExC,MAAMX,KAAK,GAAGY,IAAI,CAACC,GAAG,CAACN,IAAI,CAACP,KAAK,EAAE,CAAC,CAAC;MACrC,MAAMC,MAAM,GAAGW,IAAI,CAACC,GAAG,CAACN,IAAI,CAACN,MAAM,EAAE,CAAC,CAAC;MAEvCC,MAAM,CAACF,KAAK,GAAGA,KAAK,GAAGS,GAAG;MAC1BP,MAAM,CAACD,MAAM,GAAGA,MAAM,GAAGQ,GAAG;MAE5BL,GAAG,CAACU,KAAK,CAACL,GAAG,EAAEA,GAAG,CAAC;MACnBP,MAAM,CAACa,KAAK,CAACf,KAAK,GAAGA,KAAK,GAAG,IAAI;MACjCE,MAAM,CAACa,KAAK,CAACd,MAAM,GAAGA,MAAM,GAAG,IAAI;MAEnCF,aAAa,CAAC;QAAEC,KAAK;QAAEC;MAAO,CAAC,CAAC;IAClC,CAAC;IAEDK,UAAU,CAAC,CAAC;IACZI,MAAM,CAACM,gBAAgB,CAAC,QAAQ,EAAEV,UAAU,CAAC;;IAE7C;IACA,MAAMW,SAAS,GAAG,EAAE;IACpB,MAAMC,aAAa,GAAG,GAAG;;IAEzB;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,aAAa,EAAEC,CAAC,EAAE,EAAE;MACtCF,SAAS,CAACG,IAAI,CAAC;QACbC,CAAC,EAAET,IAAI,CAACU,MAAM,CAAC,CAAC,GAAGxB,UAAU,CAACE,KAAK;QACnCuB,CAAC,EAAEX,IAAI,CAACU,MAAM,CAAC,CAAC,GAAGxB,UAAU,CAACG,MAAM;QACpCuB,IAAI,EAAEZ,IAAI,CAACU,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG;QAC7BG,MAAM,EAAE,CAACb,IAAI,CAACU,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG;QACnCI,MAAM,EAAE,CAACd,IAAI,CAACU,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG;QACnCK,OAAO,EAAEf,IAAI,CAACU,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;QAClCM,IAAI,EAAEhB,IAAI,CAACU,MAAM,CAAC,CAAC,GAAG,GAAG;QACzBO,OAAO,EAAEjB,IAAI,CAACU,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;MACjC,CAAC,CAAC;IACJ;;IAEA;IACA,IAAIQ,IAAI,GAAG,CAAC;IACZ,MAAMC,OAAO,GAAGA,CAAA,KAAM;MACpBD,IAAI,IAAI,IAAI;;MAEZ;MACA1B,GAAG,CAAC4B,SAAS,GAAG,qBAAqB;MACrC5B,GAAG,CAAC6B,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAEnC,UAAU,CAACE,KAAK,EAAEF,UAAU,CAACG,MAAM,CAAC;;MAEvD;MACA,MAAMiC,SAAS,GAAG9B,GAAG,CAAC+B,oBAAoB,CACxCrC,UAAU,CAACE,KAAK,GAAG,GAAG,EACtBF,UAAU,CAACG,MAAM,GAAG,GAAG,EACvB,CAAC,EACDH,UAAU,CAACE,KAAK,GAAG,GAAG,EACtBF,UAAU,CAACG,MAAM,GAAG,GAAG,EACvBH,UAAU,CAACE,KAAK,GAAG,GACrB,CAAC;MACDkC,SAAS,CAACE,YAAY,CAAC,CAAC,EAAE,qBAAqB,CAAC;MAChDF,SAAS,CAACE,YAAY,CAAC,GAAG,EAAE,uBAAuB,CAAC;MACpDF,SAAS,CAACE,YAAY,CAAC,GAAG,EAAE,qBAAqB,CAAC;MAClDF,SAAS,CAACE,YAAY,CAAC,CAAC,EAAE,aAAa,CAAC;MAExChC,GAAG,CAAC4B,SAAS,GAAGE,SAAS;MACzB9B,GAAG,CAAC6B,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAEnC,UAAU,CAACE,KAAK,EAAEF,UAAU,CAACG,MAAM,CAAC;;MAEvD;MACA,MAAMoC,SAAS,GAAGjC,GAAG,CAAC+B,oBAAoB,CACxCrC,UAAU,CAACE,KAAK,GAAG,GAAG,GAAGY,IAAI,CAAC0B,GAAG,CAACR,IAAI,CAAC,GAAG,EAAE,EAC5ChC,UAAU,CAACG,MAAM,GAAG,GAAG,GAAGW,IAAI,CAAC2B,GAAG,CAACT,IAAI,GAAG,GAAG,CAAC,GAAG,EAAE,EACnD,CAAC,EACDhC,UAAU,CAACE,KAAK,GAAG,GAAG,EACtBF,UAAU,CAACG,MAAM,GAAG,GAAG,EACvBH,UAAU,CAACE,KAAK,GAAG,GACrB,CAAC;MACDqC,SAAS,CAACD,YAAY,CAAC,CAAC,EAAE,sBAAsB,CAAC;MACjDC,SAAS,CAACD,YAAY,CAAC,GAAG,EAAE,uBAAuB,CAAC;MACpDC,SAAS,CAACD,YAAY,CAAC,GAAG,EAAE,oBAAoB,CAAC;MACjDC,SAAS,CAACD,YAAY,CAAC,CAAC,EAAE,aAAa,CAAC;MAExChC,GAAG,CAACoC,wBAAwB,GAAG,UAAU;MACzCpC,GAAG,CAAC4B,SAAS,GAAGK,SAAS;MACzBjC,GAAG,CAAC6B,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAEnC,UAAU,CAACE,KAAK,EAAEF,UAAU,CAACG,MAAM,CAAC;MACvDG,GAAG,CAACoC,wBAAwB,GAAG,aAAa;;MAE5C;MACAvB,SAAS,CAACwB,OAAO,CAAC,CAACC,QAAQ,EAAEC,KAAK,KAAK;QACrC;QACAD,QAAQ,CAACrB,CAAC,IAAIqB,QAAQ,CAACjB,MAAM,GAAGb,IAAI,CAAC0B,GAAG,CAACR,IAAI,GAAGa,KAAK,GAAG,GAAG,CAAC,GAAG,GAAG;QAClED,QAAQ,CAACnB,CAAC,IAAImB,QAAQ,CAAChB,MAAM,GAAGd,IAAI,CAAC2B,GAAG,CAACT,IAAI,GAAGa,KAAK,GAAG,GAAG,CAAC,GAAG,GAAG;QAClED,QAAQ,CAACd,IAAI,EAAE;;QAEf;QACA,IAAIc,QAAQ,CAACrB,CAAC,GAAG,CAAC,EAAEqB,QAAQ,CAACrB,CAAC,GAAGvB,UAAU,CAACE,KAAK;QACjD,IAAI0C,QAAQ,CAACrB,CAAC,GAAGvB,UAAU,CAACE,KAAK,EAAE0C,QAAQ,CAACrB,CAAC,GAAG,CAAC;QACjD,IAAIqB,QAAQ,CAACnB,CAAC,GAAG,CAAC,EAAEmB,QAAQ,CAACnB,CAAC,GAAGzB,UAAU,CAACG,MAAM;QAClD,IAAIyC,QAAQ,CAACnB,CAAC,GAAGzB,UAAU,CAACG,MAAM,EAAEyC,QAAQ,CAACnB,CAAC,GAAG,CAAC;;QAElD;QACA,IAAImB,QAAQ,CAACd,IAAI,GAAGc,QAAQ,CAACb,OAAO,EAAE;UACpCa,QAAQ,CAACrB,CAAC,GAAGT,IAAI,CAACU,MAAM,CAAC,CAAC,GAAGxB,UAAU,CAACE,KAAK;UAC7C0C,QAAQ,CAACnB,CAAC,GAAGX,IAAI,CAACU,MAAM,CAAC,CAAC,GAAGxB,UAAU,CAACG,MAAM;UAC9CyC,QAAQ,CAACd,IAAI,GAAG,CAAC;UACjBc,QAAQ,CAACf,OAAO,GAAGf,IAAI,CAACU,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;QAC9C;;QAEA;QACA,MAAMsB,KAAK,GAAGF,QAAQ,CAACf,OAAO,IAAI,CAAC,GAAGe,QAAQ,CAACd,IAAI,GAAGc,QAAQ,CAACb,OAAO,CAAC;QACvEzB,GAAG,CAAC4B,SAAS,GAAG,uBAAuBY,KAAK,GAAG,GAAG,GAAG;QACrDxC,GAAG,CAACyC,SAAS,CAAC,CAAC;QACfzC,GAAG,CAAC0C,GAAG,CAACJ,QAAQ,CAACrB,CAAC,EAAEqB,QAAQ,CAACnB,CAAC,EAAEmB,QAAQ,CAAClB,IAAI,EAAE,CAAC,EAAEZ,IAAI,CAACmC,EAAE,GAAG,CAAC,CAAC;QAC9D3C,GAAG,CAAC4C,IAAI,CAAC,CAAC;MACZ,CAAC,CAAC;;MAEF;MACA,MAAMC,SAAS,GAAG7C,GAAG,CAAC8C,YAAY,CAAC,CAAC,EAAE,CAAC,EAAEpD,UAAU,CAACE,KAAK,EAAEF,UAAU,CAACG,MAAM,CAAC;MAC7E,MAAMkD,IAAI,GAAGF,SAAS,CAACE,IAAI;MAE3B,KAAK,IAAIhC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgC,IAAI,CAACC,MAAM,EAAEjC,CAAC,IAAI,CAAC,EAAE;QACvC,IAAIP,IAAI,CAACU,MAAM,CAAC,CAAC,GAAG,IAAI,EAAE;UACxB,MAAM+B,KAAK,GAAGzC,IAAI,CAACU,MAAM,CAAC,CAAC,GAAG,EAAE;UAChC6B,IAAI,CAAChC,CAAC,CAAC,IAAIkC,KAAK,CAAC,CAAK;UACtBF,IAAI,CAAChC,CAAC,GAAG,CAAC,CAAC,IAAIkC,KAAK,CAAC,CAAC;UACtBF,IAAI,CAAChC,CAAC,GAAG,CAAC,CAAC,IAAIkC,KAAK,CAAC,CAAC;QACxB;MACF;MAEAjD,GAAG,CAACkD,YAAY,CAACL,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;MAEjCpD,YAAY,CAACM,OAAO,GAAGoD,qBAAqB,CAACxB,OAAO,CAAC;IACvD,CAAC;IAEDA,OAAO,CAAC,CAAC;IAET,OAAO,MAAM;MACXrB,MAAM,CAAC8C,mBAAmB,CAAC,QAAQ,EAAElD,UAAU,CAAC;MAChD,IAAIT,YAAY,CAACM,OAAO,EAAE;QACxBsD,oBAAoB,CAAC5D,YAAY,CAACM,OAAO,CAAC;MAC5C;IACF,CAAC;EACH,CAAC,EAAE,CAACL,UAAU,CAACE,KAAK,EAAEF,UAAU,CAACG,MAAM,CAAC,CAAC;EAEzC,oBACET,OAAA;IAAKE,SAAS,EAAE,uBAAuBA,SAAS,EAAG;IAAAgE,QAAA,gBACjDlE,OAAA;MACEmE,GAAG,EAAE/D,SAAU;MACfF,SAAS,EAAC;IAAkB;MAAAkE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CAAC,eACFvE,OAAA;MAAKE,SAAS,EAAC;IAAmB;MAAAkE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAClC,CAAC;AAEV,CAAC;AAACpE,EAAA,CAlKIF,QAAQ;AAAAuE,EAAA,GAARvE,QAAQ;AAoKd,eAAeA,QAAQ;AAAC,IAAAuE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}