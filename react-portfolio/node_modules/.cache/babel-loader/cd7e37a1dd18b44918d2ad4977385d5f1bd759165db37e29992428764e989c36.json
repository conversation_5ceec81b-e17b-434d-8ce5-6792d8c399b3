{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/DarkVeil.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from 'react';\nimport { Renderer, Camera, Transform, Plane, Program, Mesh, Vec2 } from 'ogl';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DarkVeil = ({\n  className = \"\"\n}) => {\n  _s();\n  const canvasRef = useRef(null);\n  const rendererRef = useRef(null);\n  const meshRef = useRef(null);\n  const mouseRef = useRef(new Vec2());\n  const targetRef = useRef(new Vec2());\n  useEffect(() => {\n    if (!canvasRef.current) return;\n\n    // Create renderer\n    const renderer = new Renderer({\n      canvas: canvasRef.current,\n      width: window.innerWidth,\n      height: window.innerHeight,\n      dpr: Math.min(window.devicePixelRatio, 2),\n      alpha: true\n    });\n    rendererRef.current = renderer;\n    const gl = renderer.gl;\n    gl.clearColor(0, 0, 0, 0);\n\n    // Create camera\n    const camera = new Camera(gl, {\n      fov: 45\n    });\n    camera.position.z = 5;\n\n    // Create scene\n    const scene = new Transform();\n\n    // Vertex shader\n    const vertex = `\n      attribute vec2 uv;\n      attribute vec2 position;\n      uniform mat4 modelViewMatrix;\n      uniform mat4 projectionMatrix;\n      varying vec2 vUv;\n      void main() {\n        vUv = uv;\n        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 0.0, 1.0);\n      }\n    `;\n\n    // Fragment shader\n    const fragment = `\n      precision highp float;\n      uniform float uTime;\n      uniform vec2 uMouse;\n      uniform vec2 uResolution;\n      varying vec2 vUv;\n\n      // Noise function\n      float noise(vec2 st) {\n        return fract(sin(dot(st.xy, vec2(12.9898,78.233))) * 43758.5453123);\n      }\n\n      // Smooth noise\n      float smoothNoise(vec2 st) {\n        vec2 i = floor(st);\n        vec2 f = fract(st);\n        \n        float a = noise(i);\n        float b = noise(i + vec2(1.0, 0.0));\n        float c = noise(i + vec2(0.0, 1.0));\n        float d = noise(i + vec2(1.0, 1.0));\n        \n        vec2 u = f * f * (3.0 - 2.0 * f);\n        \n        return mix(a, b, u.x) + (c - a) * u.y * (1.0 - u.x) + (d - b) * u.x * u.y;\n      }\n\n      // Fractal noise\n      float fractalNoise(vec2 st) {\n        float value = 0.0;\n        float amplitude = 0.5;\n        float frequency = 0.0;\n        \n        for (int i = 0; i < 4; i++) {\n          value += amplitude * smoothNoise(st);\n          st *= 2.0;\n          amplitude *= 0.5;\n        }\n        return value;\n      }\n\n      void main() {\n        vec2 st = vUv;\n        vec2 mouse = uMouse * 0.5 + 0.5;\n        \n        // Create flowing veil effect\n        float time = uTime * 0.0005;\n        \n        // Multiple layers of noise for depth\n        float noise1 = fractalNoise(st * 3.0 + time);\n        float noise2 = fractalNoise(st * 6.0 - time * 0.7);\n        float noise3 = fractalNoise(st * 12.0 + time * 0.3);\n        \n        // Combine noises\n        float combinedNoise = noise1 * 0.5 + noise2 * 0.3 + noise3 * 0.2;\n        \n        // Create veil pattern\n        float veil = smoothstep(0.3, 0.7, combinedNoise);\n        \n        // Add mouse interaction\n        float mouseDistance = distance(st, mouse);\n        float mouseEffect = 1.0 - smoothstep(0.0, 0.3, mouseDistance);\n        veil += mouseEffect * 0.2;\n        \n        // Create gradient from center\n        float centerDistance = distance(st, vec2(0.5));\n        float gradient = 1.0 - smoothstep(0.0, 0.8, centerDistance);\n        \n        // Final color\n        vec3 color1 = vec3(0.1, 0.05, 0.2); // Dark purple\n        vec3 color2 = vec3(0.05, 0.1, 0.15); // Dark blue\n        vec3 color3 = vec3(0.0, 0.0, 0.0); // Black\n        \n        vec3 finalColor = mix(color3, color1, veil * gradient);\n        finalColor = mix(finalColor, color2, mouseEffect * 0.3);\n        \n        // Add subtle transparency\n        float alpha = veil * gradient * 0.8 + 0.1;\n        \n        gl_FragColor = vec4(finalColor, alpha);\n      }\n    `;\n\n    // Create geometry\n    const geometry = new Plane(gl, {\n      width: 10,\n      height: 10\n    });\n\n    // Create program\n    const program = new Program(gl, {\n      vertex,\n      fragment,\n      uniforms: {\n        uTime: {\n          value: 0\n        },\n        uMouse: {\n          value: mouseRef.current\n        },\n        uResolution: {\n          value: new Vec2(window.innerWidth, window.innerHeight)\n        }\n      },\n      transparent: true\n    });\n\n    // Create mesh\n    const mesh = new Mesh(gl, {\n      geometry,\n      program\n    });\n    mesh.setParent(scene);\n    meshRef.current = mesh;\n\n    // Mouse move handler\n    const handleMouseMove = e => {\n      const rect = canvasRef.current.getBoundingClientRect();\n      targetRef.current.x = (e.clientX - rect.left) / rect.width * 2 - 1;\n      targetRef.current.y = -((e.clientY - rect.top) / rect.height) * 2 + 1;\n    };\n\n    // Resize handler\n    const handleResize = () => {\n      const width = window.innerWidth;\n      const height = window.innerHeight;\n      renderer.setSize(width, height);\n      camera.perspective({\n        aspect: width / height\n      });\n      if (program.uniforms.uResolution) {\n        program.uniforms.uResolution.value.set(width, height);\n      }\n    };\n\n    // Animation loop\n    let animationId;\n    const animate = time => {\n      // Smooth mouse following\n      mouseRef.current.lerp(targetRef.current, 0.05);\n\n      // Update uniforms\n      if (program.uniforms.uTime) {\n        program.uniforms.uTime.value = time;\n      }\n      if (program.uniforms.uMouse) {\n        program.uniforms.uMouse.value = mouseRef.current;\n      }\n      renderer.render({\n        scene,\n        camera\n      });\n      animationId = requestAnimationFrame(animate);\n    };\n\n    // Event listeners\n    window.addEventListener('mousemove', handleMouseMove);\n    window.addEventListener('resize', handleResize);\n\n    // Start animation\n    animate(0);\n\n    // Cleanup\n    return () => {\n      window.removeEventListener('mousemove', handleMouseMove);\n      window.removeEventListener('resize', handleResize);\n      if (animationId) {\n        cancelAnimationFrame(animationId);\n      }\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"canvas\", {\n    ref: canvasRef,\n    className: `dark-veil ${className}`,\n    style: {\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      width: '100%',\n      height: '100%',\n      pointerEvents: 'none',\n      zIndex: 1\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 210,\n    columnNumber: 5\n  }, this);\n};\n_s(DarkVeil, \"b4gK/UySz6mTDcWnbBm1gV+2dEs=\");\n_c = DarkVeil;\nexport default DarkVeil;\nvar _c;\n$RefreshReg$(_c, \"DarkVeil\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "<PERSON><PERSON><PERSON>", "Camera", "Transform", "Plane", "Program", "<PERSON><PERSON>", "Vec2", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON>", "className", "_s", "canvasRef", "rendererRef", "meshRef", "mouseRef", "targetRef", "current", "renderer", "canvas", "width", "window", "innerWidth", "height", "innerHeight", "dpr", "Math", "min", "devicePixelRatio", "alpha", "gl", "clearColor", "camera", "fov", "position", "z", "scene", "vertex", "fragment", "geometry", "program", "uniforms", "uTime", "value", "uMouse", "uResolution", "transparent", "mesh", "setParent", "handleMouseMove", "e", "rect", "getBoundingClientRect", "x", "clientX", "left", "y", "clientY", "top", "handleResize", "setSize", "perspective", "aspect", "set", "animationId", "animate", "time", "lerp", "render", "requestAnimationFrame", "addEventListener", "removeEventListener", "cancelAnimationFrame", "ref", "style", "pointerEvents", "zIndex", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/DarkVeil.js"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\nimport { Renderer, Camera, Transform, Plane, Program, Mesh, Vec2 } from 'ogl';\n\nconst DarkVeil = ({ className = \"\" }) => {\n  const canvasRef = useRef(null);\n  const rendererRef = useRef(null);\n  const meshRef = useRef(null);\n  const mouseRef = useRef(new Vec2());\n  const targetRef = useRef(new Vec2());\n\n  useEffect(() => {\n    if (!canvasRef.current) return;\n\n    // Create renderer\n    const renderer = new Renderer({\n      canvas: canvasRef.current,\n      width: window.innerWidth,\n      height: window.innerHeight,\n      dpr: Math.min(window.devicePixelRatio, 2),\n      alpha: true,\n    });\n    rendererRef.current = renderer;\n\n    const gl = renderer.gl;\n    gl.clearColor(0, 0, 0, 0);\n\n    // Create camera\n    const camera = new Camera(gl, { fov: 45 });\n    camera.position.z = 5;\n\n    // Create scene\n    const scene = new Transform();\n\n    // Vertex shader\n    const vertex = `\n      attribute vec2 uv;\n      attribute vec2 position;\n      uniform mat4 modelViewMatrix;\n      uniform mat4 projectionMatrix;\n      varying vec2 vUv;\n      void main() {\n        vUv = uv;\n        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 0.0, 1.0);\n      }\n    `;\n\n    // Fragment shader\n    const fragment = `\n      precision highp float;\n      uniform float uTime;\n      uniform vec2 uMouse;\n      uniform vec2 uResolution;\n      varying vec2 vUv;\n\n      // Noise function\n      float noise(vec2 st) {\n        return fract(sin(dot(st.xy, vec2(12.9898,78.233))) * 43758.5453123);\n      }\n\n      // Smooth noise\n      float smoothNoise(vec2 st) {\n        vec2 i = floor(st);\n        vec2 f = fract(st);\n        \n        float a = noise(i);\n        float b = noise(i + vec2(1.0, 0.0));\n        float c = noise(i + vec2(0.0, 1.0));\n        float d = noise(i + vec2(1.0, 1.0));\n        \n        vec2 u = f * f * (3.0 - 2.0 * f);\n        \n        return mix(a, b, u.x) + (c - a) * u.y * (1.0 - u.x) + (d - b) * u.x * u.y;\n      }\n\n      // Fractal noise\n      float fractalNoise(vec2 st) {\n        float value = 0.0;\n        float amplitude = 0.5;\n        float frequency = 0.0;\n        \n        for (int i = 0; i < 4; i++) {\n          value += amplitude * smoothNoise(st);\n          st *= 2.0;\n          amplitude *= 0.5;\n        }\n        return value;\n      }\n\n      void main() {\n        vec2 st = vUv;\n        vec2 mouse = uMouse * 0.5 + 0.5;\n        \n        // Create flowing veil effect\n        float time = uTime * 0.0005;\n        \n        // Multiple layers of noise for depth\n        float noise1 = fractalNoise(st * 3.0 + time);\n        float noise2 = fractalNoise(st * 6.0 - time * 0.7);\n        float noise3 = fractalNoise(st * 12.0 + time * 0.3);\n        \n        // Combine noises\n        float combinedNoise = noise1 * 0.5 + noise2 * 0.3 + noise3 * 0.2;\n        \n        // Create veil pattern\n        float veil = smoothstep(0.3, 0.7, combinedNoise);\n        \n        // Add mouse interaction\n        float mouseDistance = distance(st, mouse);\n        float mouseEffect = 1.0 - smoothstep(0.0, 0.3, mouseDistance);\n        veil += mouseEffect * 0.2;\n        \n        // Create gradient from center\n        float centerDistance = distance(st, vec2(0.5));\n        float gradient = 1.0 - smoothstep(0.0, 0.8, centerDistance);\n        \n        // Final color\n        vec3 color1 = vec3(0.1, 0.05, 0.2); // Dark purple\n        vec3 color2 = vec3(0.05, 0.1, 0.15); // Dark blue\n        vec3 color3 = vec3(0.0, 0.0, 0.0); // Black\n        \n        vec3 finalColor = mix(color3, color1, veil * gradient);\n        finalColor = mix(finalColor, color2, mouseEffect * 0.3);\n        \n        // Add subtle transparency\n        float alpha = veil * gradient * 0.8 + 0.1;\n        \n        gl_FragColor = vec4(finalColor, alpha);\n      }\n    `;\n\n    // Create geometry\n    const geometry = new Plane(gl, {\n      width: 10,\n      height: 10,\n    });\n\n    // Create program\n    const program = new Program(gl, {\n      vertex,\n      fragment,\n      uniforms: {\n        uTime: { value: 0 },\n        uMouse: { value: mouseRef.current },\n        uResolution: { value: new Vec2(window.innerWidth, window.innerHeight) },\n      },\n      transparent: true,\n    });\n\n    // Create mesh\n    const mesh = new Mesh(gl, { geometry, program });\n    mesh.setParent(scene);\n    meshRef.current = mesh;\n\n    // Mouse move handler\n    const handleMouseMove = (e) => {\n      const rect = canvasRef.current.getBoundingClientRect();\n      targetRef.current.x = ((e.clientX - rect.left) / rect.width) * 2 - 1;\n      targetRef.current.y = -((e.clientY - rect.top) / rect.height) * 2 + 1;\n    };\n\n    // Resize handler\n    const handleResize = () => {\n      const width = window.innerWidth;\n      const height = window.innerHeight;\n      \n      renderer.setSize(width, height);\n      camera.perspective({ aspect: width / height });\n      \n      if (program.uniforms.uResolution) {\n        program.uniforms.uResolution.value.set(width, height);\n      }\n    };\n\n    // Animation loop\n    let animationId;\n    const animate = (time) => {\n      // Smooth mouse following\n      mouseRef.current.lerp(targetRef.current, 0.05);\n      \n      // Update uniforms\n      if (program.uniforms.uTime) {\n        program.uniforms.uTime.value = time;\n      }\n      if (program.uniforms.uMouse) {\n        program.uniforms.uMouse.value = mouseRef.current;\n      }\n\n      renderer.render({ scene, camera });\n      animationId = requestAnimationFrame(animate);\n    };\n\n    // Event listeners\n    window.addEventListener('mousemove', handleMouseMove);\n    window.addEventListener('resize', handleResize);\n\n    // Start animation\n    animate(0);\n\n    // Cleanup\n    return () => {\n      window.removeEventListener('mousemove', handleMouseMove);\n      window.removeEventListener('resize', handleResize);\n      if (animationId) {\n        cancelAnimationFrame(animationId);\n      }\n    };\n  }, []);\n\n  return (\n    <canvas\n      ref={canvasRef}\n      className={`dark-veil ${className}`}\n      style={{\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        width: '100%',\n        height: '100%',\n        pointerEvents: 'none',\n        zIndex: 1,\n      }}\n    />\n  );\n};\n\nexport default DarkVeil;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAChD,SAASC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,KAAK,EAAEC,OAAO,EAAEC,IAAI,EAAEC,IAAI,QAAQ,KAAK;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9E,MAAMC,QAAQ,GAAGA,CAAC;EAAEC,SAAS,GAAG;AAAG,CAAC,KAAK;EAAAC,EAAA;EACvC,MAAMC,SAAS,GAAGb,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMc,WAAW,GAAGd,MAAM,CAAC,IAAI,CAAC;EAChC,MAAMe,OAAO,GAAGf,MAAM,CAAC,IAAI,CAAC;EAC5B,MAAMgB,QAAQ,GAAGhB,MAAM,CAAC,IAAIO,IAAI,CAAC,CAAC,CAAC;EACnC,MAAMU,SAAS,GAAGjB,MAAM,CAAC,IAAIO,IAAI,CAAC,CAAC,CAAC;EAEpCR,SAAS,CAAC,MAAM;IACd,IAAI,CAACc,SAAS,CAACK,OAAO,EAAE;;IAExB;IACA,MAAMC,QAAQ,GAAG,IAAIlB,QAAQ,CAAC;MAC5BmB,MAAM,EAAEP,SAAS,CAACK,OAAO;MACzBG,KAAK,EAAEC,MAAM,CAACC,UAAU;MACxBC,MAAM,EAAEF,MAAM,CAACG,WAAW;MAC1BC,GAAG,EAAEC,IAAI,CAACC,GAAG,CAACN,MAAM,CAACO,gBAAgB,EAAE,CAAC,CAAC;MACzCC,KAAK,EAAE;IACT,CAAC,CAAC;IACFhB,WAAW,CAACI,OAAO,GAAGC,QAAQ;IAE9B,MAAMY,EAAE,GAAGZ,QAAQ,CAACY,EAAE;IACtBA,EAAE,CAACC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;IAEzB;IACA,MAAMC,MAAM,GAAG,IAAI/B,MAAM,CAAC6B,EAAE,EAAE;MAAEG,GAAG,EAAE;IAAG,CAAC,CAAC;IAC1CD,MAAM,CAACE,QAAQ,CAACC,CAAC,GAAG,CAAC;;IAErB;IACA,MAAMC,KAAK,GAAG,IAAIlC,SAAS,CAAC,CAAC;;IAE7B;IACA,MAAMmC,MAAM,GAAG;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;;IAED;IACA,MAAMC,QAAQ,GAAG;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;;IAED;IACA,MAAMC,QAAQ,GAAG,IAAIpC,KAAK,CAAC2B,EAAE,EAAE;MAC7BV,KAAK,EAAE,EAAE;MACTG,MAAM,EAAE;IACV,CAAC,CAAC;;IAEF;IACA,MAAMiB,OAAO,GAAG,IAAIpC,OAAO,CAAC0B,EAAE,EAAE;MAC9BO,MAAM;MACNC,QAAQ;MACRG,QAAQ,EAAE;QACRC,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAE,CAAC;QACnBC,MAAM,EAAE;UAAED,KAAK,EAAE5B,QAAQ,CAACE;QAAQ,CAAC;QACnC4B,WAAW,EAAE;UAAEF,KAAK,EAAE,IAAIrC,IAAI,CAACe,MAAM,CAACC,UAAU,EAAED,MAAM,CAACG,WAAW;QAAE;MACxE,CAAC;MACDsB,WAAW,EAAE;IACf,CAAC,CAAC;;IAEF;IACA,MAAMC,IAAI,GAAG,IAAI1C,IAAI,CAACyB,EAAE,EAAE;MAAES,QAAQ;MAAEC;IAAQ,CAAC,CAAC;IAChDO,IAAI,CAACC,SAAS,CAACZ,KAAK,CAAC;IACrBtB,OAAO,CAACG,OAAO,GAAG8B,IAAI;;IAEtB;IACA,MAAME,eAAe,GAAIC,CAAC,IAAK;MAC7B,MAAMC,IAAI,GAAGvC,SAAS,CAACK,OAAO,CAACmC,qBAAqB,CAAC,CAAC;MACtDpC,SAAS,CAACC,OAAO,CAACoC,CAAC,GAAI,CAACH,CAAC,CAACI,OAAO,GAAGH,IAAI,CAACI,IAAI,IAAIJ,IAAI,CAAC/B,KAAK,GAAI,CAAC,GAAG,CAAC;MACpEJ,SAAS,CAACC,OAAO,CAACuC,CAAC,GAAG,EAAE,CAACN,CAAC,CAACO,OAAO,GAAGN,IAAI,CAACO,GAAG,IAAIP,IAAI,CAAC5B,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC;IACvE,CAAC;;IAED;IACA,MAAMoC,YAAY,GAAGA,CAAA,KAAM;MACzB,MAAMvC,KAAK,GAAGC,MAAM,CAACC,UAAU;MAC/B,MAAMC,MAAM,GAAGF,MAAM,CAACG,WAAW;MAEjCN,QAAQ,CAAC0C,OAAO,CAACxC,KAAK,EAAEG,MAAM,CAAC;MAC/BS,MAAM,CAAC6B,WAAW,CAAC;QAAEC,MAAM,EAAE1C,KAAK,GAAGG;MAAO,CAAC,CAAC;MAE9C,IAAIiB,OAAO,CAACC,QAAQ,CAACI,WAAW,EAAE;QAChCL,OAAO,CAACC,QAAQ,CAACI,WAAW,CAACF,KAAK,CAACoB,GAAG,CAAC3C,KAAK,EAAEG,MAAM,CAAC;MACvD;IACF,CAAC;;IAED;IACA,IAAIyC,WAAW;IACf,MAAMC,OAAO,GAAIC,IAAI,IAAK;MACxB;MACAnD,QAAQ,CAACE,OAAO,CAACkD,IAAI,CAACnD,SAAS,CAACC,OAAO,EAAE,IAAI,CAAC;;MAE9C;MACA,IAAIuB,OAAO,CAACC,QAAQ,CAACC,KAAK,EAAE;QAC1BF,OAAO,CAACC,QAAQ,CAACC,KAAK,CAACC,KAAK,GAAGuB,IAAI;MACrC;MACA,IAAI1B,OAAO,CAACC,QAAQ,CAACG,MAAM,EAAE;QAC3BJ,OAAO,CAACC,QAAQ,CAACG,MAAM,CAACD,KAAK,GAAG5B,QAAQ,CAACE,OAAO;MAClD;MAEAC,QAAQ,CAACkD,MAAM,CAAC;QAAEhC,KAAK;QAAEJ;MAAO,CAAC,CAAC;MAClCgC,WAAW,GAAGK,qBAAqB,CAACJ,OAAO,CAAC;IAC9C,CAAC;;IAED;IACA5C,MAAM,CAACiD,gBAAgB,CAAC,WAAW,EAAErB,eAAe,CAAC;IACrD5B,MAAM,CAACiD,gBAAgB,CAAC,QAAQ,EAAEX,YAAY,CAAC;;IAE/C;IACAM,OAAO,CAAC,CAAC,CAAC;;IAEV;IACA,OAAO,MAAM;MACX5C,MAAM,CAACkD,mBAAmB,CAAC,WAAW,EAAEtB,eAAe,CAAC;MACxD5B,MAAM,CAACkD,mBAAmB,CAAC,QAAQ,EAAEZ,YAAY,CAAC;MAClD,IAAIK,WAAW,EAAE;QACfQ,oBAAoB,CAACR,WAAW,CAAC;MACnC;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACExD,OAAA;IACEiE,GAAG,EAAE7D,SAAU;IACfF,SAAS,EAAE,aAAaA,SAAS,EAAG;IACpCgE,KAAK,EAAE;MACLxC,QAAQ,EAAE,UAAU;MACpBwB,GAAG,EAAE,CAAC;MACNH,IAAI,EAAE,CAAC;MACPnC,KAAK,EAAE,MAAM;MACbG,MAAM,EAAE,MAAM;MACdoD,aAAa,EAAE,MAAM;MACrBC,MAAM,EAAE;IACV;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEN,CAAC;AAACrE,EAAA,CA5NIF,QAAQ;AAAAwE,EAAA,GAARxE,QAAQ;AA8Nd,eAAeA,QAAQ;AAAC,IAAAwE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}