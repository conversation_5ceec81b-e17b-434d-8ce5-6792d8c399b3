{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/Hero.js\";\nimport React from 'react';\nimport TextPressure from './TextPressure';\nimport ShinyText from './ShinyText';\nimport Navigation from './Navigation';\nimport ScrollFloat from './ScrollFloat';\nimport DarkV<PERSON> from './DarkVeil';\nimport './Hero.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Hero = () => {\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"hero\",\n    id: \"home\",\n    children: [/*#__PURE__*/_jsxDEV(Navigation, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"hero-content\",\n      children: [/*#__PURE__*/_jsxDEV(ScrollFloat, {\n        direction: \"up\",\n        duration: 0.8,\n        delay: 0.3,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hero-title\",\n          children: /*#__PURE__*/_jsxDEV(TextPressure, {\n            text: \"DevDoses\",\n            fontSize: \"6rem\",\n            pressureIntensity: 0.5,\n            initialOpacity: 1,\n            className: \"main-title\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 19,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ScrollFloat, {\n        direction: \"up\",\n        duration: 0.7,\n        delay: 0.6,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hero-subtitle\",\n          children: /*#__PURE__*/_jsxDEV(ShinyText, {\n            text: \"Creative Developer Portfolio\",\n            fontSize: \"1.5rem\",\n            shimmerDuration: 3000,\n            className: \"subtitle\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ScrollFloat, {\n        direction: \"up\",\n        duration: 0.6,\n        delay: 0.9,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hero-description\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Building amazing digital experiences with modern web technologies\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"hero-dark-veil\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"veil-layer veil-layer-1\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"veil-layer veil-layer-2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"veil-layer veil-layer-3\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"veil-noise\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 5\n  }, this);\n};\n_c = Hero;\nexport default Hero;\nvar _c;\n$RefreshReg$(_c, \"Hero\");", "map": {"version": 3, "names": ["React", "TextPressure", "ShinyText", "Navigation", "ScrollFloat", "<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Hero", "className", "id", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "direction", "duration", "delay", "text", "fontSize", "pressureIntensity", "initialOpacity", "shimmerDuration", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/Hero.js"], "sourcesContent": ["import React from 'react';\nimport TextPressure from './TextPressure';\nimport ShinyText from './ShinyText';\nimport Navigation from './Navigation';\nimport ScrollFloat from './ScrollFloat';\nimport DarkVeil from './DarkVeil';\nimport './Hero.css';\n\nconst Hero = () => {\n  return (\n    <section className=\"hero\" id=\"home\">\n      {/* Navigation Menu */}\n      <Navigation />\n\n      {/* Main Content */}\n      <div className=\"hero-content\">\n        <ScrollFloat direction=\"up\" duration={0.8} delay={0.3}>\n          <div className=\"hero-title\">\n            <TextPressure\n              text=\"DevDoses\"\n              fontSize=\"6rem\"\n              pressureIntensity={0.5}\n              initialOpacity={1}\n              className=\"main-title\"\n            />\n          </div>\n        </ScrollFloat>\n\n        <ScrollFloat direction=\"up\" duration={0.7} delay={0.6}>\n          <div className=\"hero-subtitle\">\n            <ShinyText\n              text=\"Creative Developer Portfolio\"\n              fontSize=\"1.5rem\"\n              shimmerDuration={3000}\n              className=\"subtitle\"\n            />\n          </div>\n        </ScrollFloat>\n\n        <ScrollFloat direction=\"up\" duration={0.6} delay={0.9}>\n          <div className=\"hero-description\">\n            <p>Building amazing digital experiences with modern web technologies</p>\n          </div>\n        </ScrollFloat>\n      </div>\n\n      {/* Background Elements */}\n      <div className=\"hero-dark-veil\">\n        <div className=\"veil-layer veil-layer-1\"></div>\n        <div className=\"veil-layer veil-layer-2\"></div>\n        <div className=\"veil-layer veil-layer-3\"></div>\n        <div className=\"veil-noise\"></div>\n      </div>\n    </section>\n  );\n};\n\nexport default Hero;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAO,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpB,MAAMC,IAAI,GAAGA,CAAA,KAAM;EACjB,oBACED,OAAA;IAASE,SAAS,EAAC,MAAM;IAACC,EAAE,EAAC,MAAM;IAAAC,QAAA,gBAEjCJ,OAAA,CAACJ,UAAU;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGdR,OAAA;MAAKE,SAAS,EAAC,cAAc;MAAAE,QAAA,gBAC3BJ,OAAA,CAACH,WAAW;QAACY,SAAS,EAAC,IAAI;QAACC,QAAQ,EAAE,GAAI;QAACC,KAAK,EAAE,GAAI;QAAAP,QAAA,eACpDJ,OAAA;UAAKE,SAAS,EAAC,YAAY;UAAAE,QAAA,eACzBJ,OAAA,CAACN,YAAY;YACXkB,IAAI,EAAC,UAAU;YACfC,QAAQ,EAAC,MAAM;YACfC,iBAAiB,EAAE,GAAI;YACvBC,cAAc,EAAE,CAAE;YAClBb,SAAS,EAAC;UAAY;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eAEdR,OAAA,CAACH,WAAW;QAACY,SAAS,EAAC,IAAI;QAACC,QAAQ,EAAE,GAAI;QAACC,KAAK,EAAE,GAAI;QAAAP,QAAA,eACpDJ,OAAA;UAAKE,SAAS,EAAC,eAAe;UAAAE,QAAA,eAC5BJ,OAAA,CAACL,SAAS;YACRiB,IAAI,EAAC,8BAA8B;YACnCC,QAAQ,EAAC,QAAQ;YACjBG,eAAe,EAAE,IAAK;YACtBd,SAAS,EAAC;UAAU;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eAEdR,OAAA,CAACH,WAAW;QAACY,SAAS,EAAC,IAAI;QAACC,QAAQ,EAAE,GAAI;QAACC,KAAK,EAAE,GAAI;QAAAP,QAAA,eACpDJ,OAAA;UAAKE,SAAS,EAAC,kBAAkB;UAAAE,QAAA,eAC/BJ,OAAA;YAAAI,QAAA,EAAG;UAAiE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CAAC,eAGNR,OAAA;MAAKE,SAAS,EAAC,gBAAgB;MAAAE,QAAA,gBAC7BJ,OAAA;QAAKE,SAAS,EAAC;MAAyB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC/CR,OAAA;QAAKE,SAAS,EAAC;MAAyB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC/CR,OAAA;QAAKE,SAAS,EAAC;MAAyB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC/CR,OAAA;QAAKE,SAAS,EAAC;MAAY;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACS,EAAA,GA/CIhB,IAAI;AAiDV,eAAeA,IAAI;AAAC,IAAAgB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}