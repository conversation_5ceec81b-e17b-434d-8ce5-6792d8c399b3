{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/DarkVeil.js\",\n  _s = $RefreshSig$();\nimport React, { useRef, useEffect } from 'react';\nimport { Renderer, Camera, Transform, Plane, Program, Mesh, Vec2 } from 'ogl';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DarkVeil = ({\n  className = \"\"\n}) => {\n  _s();\n  const canvasRef = useRef();\n  const rendererRef = useRef();\n  const animationRef = useRef();\n  useEffect(() => {\n    if (!canvasRef.current) return;\n\n    // Create renderer\n    const renderer = new Renderer({\n      canvas: canvasRef.current,\n      width: window.innerWidth,\n      height: window.innerHeight,\n      dpr: Math.min(window.devicePixelRatio, 2),\n      alpha: true\n    });\n    rendererRef.current = renderer;\n    const gl = renderer.gl;\n    gl.clearColor(0, 0, 0, 0);\n\n    // Create camera\n    const camera = new Camera(gl, {\n      fov: 35\n    });\n    camera.position.set(0, 0, 5);\n\n    // Create scene\n    const scene = new Transform();\n\n    // Vertex shader\n    const vertex = `\n      attribute vec2 uv;\n      attribute vec2 position;\n      uniform mat4 modelViewMatrix;\n      uniform mat4 projectionMatrix;\n      varying vec2 vUv;\n      void main() {\n        vUv = uv;\n        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 0.0, 1.0);\n      }\n    `;\n\n    // Fragment shader\n    const fragment = `\n      precision highp float;\n      uniform float uTime;\n      uniform vec2 uResolution;\n      varying vec2 vUv;\n\n      // Noise function\n      float noise(vec2 p) {\n        return fract(sin(dot(p, vec2(12.9898, 78.233))) * 43758.5453);\n      }\n\n      // Smooth noise\n      float smoothNoise(vec2 p) {\n        vec2 i = floor(p);\n        vec2 f = fract(p);\n        f = f * f * (3.0 - 2.0 * f);\n        \n        float a = noise(i);\n        float b = noise(i + vec2(1.0, 0.0));\n        float c = noise(i + vec2(0.0, 1.0));\n        float d = noise(i + vec2(1.0, 1.0));\n        \n        return mix(mix(a, b, f.x), mix(c, d, f.x), f.y);\n      }\n\n      // Fractal noise\n      float fractalNoise(vec2 p) {\n        float value = 0.0;\n        float amplitude = 0.5;\n        float frequency = 1.0;\n        \n        for(int i = 0; i < 4; i++) {\n          value += amplitude * smoothNoise(p * frequency);\n          amplitude *= 0.5;\n          frequency *= 2.0;\n        }\n        \n        return value;\n      }\n\n      void main() {\n        vec2 uv = vUv;\n        vec2 p = uv * 2.0 - 1.0;\n        p.x *= uResolution.x / uResolution.y;\n        \n        // Create moving noise\n        vec2 noiseCoord = p * 3.0 + uTime * 0.1;\n        float n1 = fractalNoise(noiseCoord);\n        float n2 = fractalNoise(noiseCoord + vec2(100.0));\n        \n        // Create veil effect\n        float veil = smoothstep(0.0, 1.0, length(p) * 0.8);\n        veil = pow(veil, 2.0);\n        \n        // Combine noise with veil\n        float finalNoise = mix(n1, n2, sin(uTime * 0.5) * 0.5 + 0.5);\n        finalNoise = smoothstep(0.3, 0.7, finalNoise);\n        \n        // Create dark veil with subtle variations\n        float darkness = 1.0 - veil * 0.3;\n        darkness *= (1.0 - finalNoise * 0.1);\n        \n        // Add some color variation\n        vec3 color = vec3(0.05, 0.08, 0.12) * darkness;\n        color += vec3(0.02, 0.03, 0.05) * finalNoise * (1.0 - veil);\n        \n        gl_FragColor = vec4(color, darkness * 0.9);\n      }\n    `;\n\n    // Create geometry\n    const geometry = new Plane(gl, {\n      width: 2,\n      height: 2\n    });\n\n    // Create program with error handling\n    let program;\n    try {\n      program = new Program(gl, {\n        vertex,\n        fragment,\n        uniforms: {\n          uTime: {\n            value: 0\n          },\n          uResolution: {\n            value: new Vec2(window.innerWidth, window.innerHeight)\n          }\n        },\n        transparent: true\n      });\n    } catch (error) {\n      console.error('Failed to create WebGL program:', error);\n      return;\n    }\n\n    // Create mesh\n    const mesh = new Mesh(gl, {\n      geometry,\n      program\n    });\n    mesh.setParent(scene);\n\n    // Animation loop\n    const animate = time => {\n      program.uniforms.uTime.value = time * 0.001;\n      renderer.render({\n        scene,\n        camera\n      });\n      animationRef.current = requestAnimationFrame(animate);\n    };\n    animate(0);\n\n    // Handle resize\n    const handleResize = () => {\n      const width = window.innerWidth;\n      const height = window.innerHeight;\n      renderer.setSize(width, height);\n      camera.perspective({\n        aspect: width / height\n      });\n      program.uniforms.uResolution.value.set(width, height);\n    };\n    window.addEventListener('resize', handleResize);\n    return () => {\n      window.removeEventListener('resize', handleResize);\n      if (animationRef.current) {\n        cancelAnimationFrame(animationRef.current);\n      }\n      if (rendererRef.current) {\n        var _rendererRef$current$;\n        (_rendererRef$current$ = rendererRef.current.gl.getExtension('WEBGL_lose_context')) === null || _rendererRef$current$ === void 0 ? void 0 : _rendererRef$current$.loseContext();\n      }\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"canvas\", {\n    ref: canvasRef,\n    className: `dark-veil ${className}`,\n    style: {\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      width: '100%',\n      height: '100%',\n      pointerEvents: 'none',\n      zIndex: 0\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 177,\n    columnNumber: 5\n  }, this);\n};\n_s(DarkVeil, \"/2PXwoRPtedWUdwpv8ha3uOXCYg=\");\n_c = DarkVeil;\nexport default DarkVeil;\nvar _c;\n$RefreshReg$(_c, \"DarkVeil\");", "map": {"version": 3, "names": ["React", "useRef", "useEffect", "<PERSON><PERSON><PERSON>", "Camera", "Transform", "Plane", "Program", "<PERSON><PERSON>", "Vec2", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON>", "className", "_s", "canvasRef", "rendererRef", "animationRef", "current", "renderer", "canvas", "width", "window", "innerWidth", "height", "innerHeight", "dpr", "Math", "min", "devicePixelRatio", "alpha", "gl", "clearColor", "camera", "fov", "position", "set", "scene", "vertex", "fragment", "geometry", "program", "uniforms", "uTime", "value", "uResolution", "transparent", "error", "console", "mesh", "setParent", "animate", "time", "render", "requestAnimationFrame", "handleResize", "setSize", "perspective", "aspect", "addEventListener", "removeEventListener", "cancelAnimationFrame", "_rendererRef$current$", "getExtension", "loseContext", "ref", "style", "top", "left", "pointerEvents", "zIndex", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/DarkVeil.js"], "sourcesContent": ["import React, { useRef, useEffect } from 'react';\nimport { Renderer, Camera, Transform, Plane, Program, Mesh, Vec2 } from 'ogl';\n\nconst DarkVeil = ({ className = \"\" }) => {\n  const canvasRef = useRef();\n  const rendererRef = useRef();\n  const animationRef = useRef();\n\n  useEffect(() => {\n    if (!canvasRef.current) return;\n\n    // Create renderer\n    const renderer = new Renderer({\n      canvas: canvasRef.current,\n      width: window.innerWidth,\n      height: window.innerHeight,\n      dpr: Math.min(window.devicePixelRatio, 2),\n      alpha: true,\n    });\n    rendererRef.current = renderer;\n\n    const gl = renderer.gl;\n    gl.clearColor(0, 0, 0, 0);\n\n    // Create camera\n    const camera = new Camera(gl, { fov: 35 });\n    camera.position.set(0, 0, 5);\n\n    // Create scene\n    const scene = new Transform();\n\n    // Vertex shader\n    const vertex = `\n      attribute vec2 uv;\n      attribute vec2 position;\n      uniform mat4 modelViewMatrix;\n      uniform mat4 projectionMatrix;\n      varying vec2 vUv;\n      void main() {\n        vUv = uv;\n        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 0.0, 1.0);\n      }\n    `;\n\n    // Fragment shader\n    const fragment = `\n      precision highp float;\n      uniform float uTime;\n      uniform vec2 uResolution;\n      varying vec2 vUv;\n\n      // Noise function\n      float noise(vec2 p) {\n        return fract(sin(dot(p, vec2(12.9898, 78.233))) * 43758.5453);\n      }\n\n      // Smooth noise\n      float smoothNoise(vec2 p) {\n        vec2 i = floor(p);\n        vec2 f = fract(p);\n        f = f * f * (3.0 - 2.0 * f);\n        \n        float a = noise(i);\n        float b = noise(i + vec2(1.0, 0.0));\n        float c = noise(i + vec2(0.0, 1.0));\n        float d = noise(i + vec2(1.0, 1.0));\n        \n        return mix(mix(a, b, f.x), mix(c, d, f.x), f.y);\n      }\n\n      // Fractal noise\n      float fractalNoise(vec2 p) {\n        float value = 0.0;\n        float amplitude = 0.5;\n        float frequency = 1.0;\n        \n        for(int i = 0; i < 4; i++) {\n          value += amplitude * smoothNoise(p * frequency);\n          amplitude *= 0.5;\n          frequency *= 2.0;\n        }\n        \n        return value;\n      }\n\n      void main() {\n        vec2 uv = vUv;\n        vec2 p = uv * 2.0 - 1.0;\n        p.x *= uResolution.x / uResolution.y;\n        \n        // Create moving noise\n        vec2 noiseCoord = p * 3.0 + uTime * 0.1;\n        float n1 = fractalNoise(noiseCoord);\n        float n2 = fractalNoise(noiseCoord + vec2(100.0));\n        \n        // Create veil effect\n        float veil = smoothstep(0.0, 1.0, length(p) * 0.8);\n        veil = pow(veil, 2.0);\n        \n        // Combine noise with veil\n        float finalNoise = mix(n1, n2, sin(uTime * 0.5) * 0.5 + 0.5);\n        finalNoise = smoothstep(0.3, 0.7, finalNoise);\n        \n        // Create dark veil with subtle variations\n        float darkness = 1.0 - veil * 0.3;\n        darkness *= (1.0 - finalNoise * 0.1);\n        \n        // Add some color variation\n        vec3 color = vec3(0.05, 0.08, 0.12) * darkness;\n        color += vec3(0.02, 0.03, 0.05) * finalNoise * (1.0 - veil);\n        \n        gl_FragColor = vec4(color, darkness * 0.9);\n      }\n    `;\n\n    // Create geometry\n    const geometry = new Plane(gl, {\n      width: 2,\n      height: 2,\n    });\n\n    // Create program with error handling\n    let program;\n    try {\n      program = new Program(gl, {\n        vertex,\n        fragment,\n        uniforms: {\n          uTime: { value: 0 },\n          uResolution: { value: new Vec2(window.innerWidth, window.innerHeight) },\n        },\n        transparent: true,\n      });\n    } catch (error) {\n      console.error('Failed to create WebGL program:', error);\n      return;\n    }\n\n    // Create mesh\n    const mesh = new Mesh(gl, { geometry, program });\n    mesh.setParent(scene);\n\n    // Animation loop\n    const animate = (time) => {\n      program.uniforms.uTime.value = time * 0.001;\n      \n      renderer.render({ scene, camera });\n      animationRef.current = requestAnimationFrame(animate);\n    };\n\n    animate(0);\n\n    // Handle resize\n    const handleResize = () => {\n      const width = window.innerWidth;\n      const height = window.innerHeight;\n      \n      renderer.setSize(width, height);\n      camera.perspective({ aspect: width / height });\n      program.uniforms.uResolution.value.set(width, height);\n    };\n\n    window.addEventListener('resize', handleResize);\n\n    return () => {\n      window.removeEventListener('resize', handleResize);\n      if (animationRef.current) {\n        cancelAnimationFrame(animationRef.current);\n      }\n      if (rendererRef.current) {\n        rendererRef.current.gl.getExtension('WEBGL_lose_context')?.loseContext();\n      }\n    };\n  }, []);\n\n  return (\n    <canvas\n      ref={canvasRef}\n      className={`dark-veil ${className}`}\n      style={{\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        width: '100%',\n        height: '100%',\n        pointerEvents: 'none',\n        zIndex: 0,\n      }}\n    />\n  );\n};\n\nexport default DarkVeil;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAChD,SAASC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,KAAK,EAAEC,OAAO,EAAEC,IAAI,EAAEC,IAAI,QAAQ,KAAK;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9E,MAAMC,QAAQ,GAAGA,CAAC;EAAEC,SAAS,GAAG;AAAG,CAAC,KAAK;EAAAC,EAAA;EACvC,MAAMC,SAAS,GAAGd,MAAM,CAAC,CAAC;EAC1B,MAAMe,WAAW,GAAGf,MAAM,CAAC,CAAC;EAC5B,MAAMgB,YAAY,GAAGhB,MAAM,CAAC,CAAC;EAE7BC,SAAS,CAAC,MAAM;IACd,IAAI,CAACa,SAAS,CAACG,OAAO,EAAE;;IAExB;IACA,MAAMC,QAAQ,GAAG,IAAIhB,QAAQ,CAAC;MAC5BiB,MAAM,EAAEL,SAAS,CAACG,OAAO;MACzBG,KAAK,EAAEC,MAAM,CAACC,UAAU;MACxBC,MAAM,EAAEF,MAAM,CAACG,WAAW;MAC1BC,GAAG,EAAEC,IAAI,CAACC,GAAG,CAACN,MAAM,CAACO,gBAAgB,EAAE,CAAC,CAAC;MACzCC,KAAK,EAAE;IACT,CAAC,CAAC;IACFd,WAAW,CAACE,OAAO,GAAGC,QAAQ;IAE9B,MAAMY,EAAE,GAAGZ,QAAQ,CAACY,EAAE;IACtBA,EAAE,CAACC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;IAEzB;IACA,MAAMC,MAAM,GAAG,IAAI7B,MAAM,CAAC2B,EAAE,EAAE;MAAEG,GAAG,EAAE;IAAG,CAAC,CAAC;IAC1CD,MAAM,CAACE,QAAQ,CAACC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;IAE5B;IACA,MAAMC,KAAK,GAAG,IAAIhC,SAAS,CAAC,CAAC;;IAE7B;IACA,MAAMiC,MAAM,GAAG;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;;IAED;IACA,MAAMC,QAAQ,GAAG;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;;IAED;IACA,MAAMC,QAAQ,GAAG,IAAIlC,KAAK,CAACyB,EAAE,EAAE;MAC7BV,KAAK,EAAE,CAAC;MACRG,MAAM,EAAE;IACV,CAAC,CAAC;;IAEF;IACA,IAAIiB,OAAO;IACX,IAAI;MACFA,OAAO,GAAG,IAAIlC,OAAO,CAACwB,EAAE,EAAE;QACxBO,MAAM;QACNC,QAAQ;QACRG,QAAQ,EAAE;UACRC,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAE,CAAC;UACnBC,WAAW,EAAE;YAAED,KAAK,EAAE,IAAInC,IAAI,CAACa,MAAM,CAACC,UAAU,EAAED,MAAM,CAACG,WAAW;UAAE;QACxE,CAAC;QACDqB,WAAW,EAAE;MACf,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD;IACF;;IAEA;IACA,MAAME,IAAI,GAAG,IAAIzC,IAAI,CAACuB,EAAE,EAAE;MAAES,QAAQ;MAAEC;IAAQ,CAAC,CAAC;IAChDQ,IAAI,CAACC,SAAS,CAACb,KAAK,CAAC;;IAErB;IACA,MAAMc,OAAO,GAAIC,IAAI,IAAK;MACxBX,OAAO,CAACC,QAAQ,CAACC,KAAK,CAACC,KAAK,GAAGQ,IAAI,GAAG,KAAK;MAE3CjC,QAAQ,CAACkC,MAAM,CAAC;QAAEhB,KAAK;QAAEJ;MAAO,CAAC,CAAC;MAClChB,YAAY,CAACC,OAAO,GAAGoC,qBAAqB,CAACH,OAAO,CAAC;IACvD,CAAC;IAEDA,OAAO,CAAC,CAAC,CAAC;;IAEV;IACA,MAAMI,YAAY,GAAGA,CAAA,KAAM;MACzB,MAAMlC,KAAK,GAAGC,MAAM,CAACC,UAAU;MAC/B,MAAMC,MAAM,GAAGF,MAAM,CAACG,WAAW;MAEjCN,QAAQ,CAACqC,OAAO,CAACnC,KAAK,EAAEG,MAAM,CAAC;MAC/BS,MAAM,CAACwB,WAAW,CAAC;QAAEC,MAAM,EAAErC,KAAK,GAAGG;MAAO,CAAC,CAAC;MAC9CiB,OAAO,CAACC,QAAQ,CAACG,WAAW,CAACD,KAAK,CAACR,GAAG,CAACf,KAAK,EAAEG,MAAM,CAAC;IACvD,CAAC;IAEDF,MAAM,CAACqC,gBAAgB,CAAC,QAAQ,EAAEJ,YAAY,CAAC;IAE/C,OAAO,MAAM;MACXjC,MAAM,CAACsC,mBAAmB,CAAC,QAAQ,EAAEL,YAAY,CAAC;MAClD,IAAItC,YAAY,CAACC,OAAO,EAAE;QACxB2C,oBAAoB,CAAC5C,YAAY,CAACC,OAAO,CAAC;MAC5C;MACA,IAAIF,WAAW,CAACE,OAAO,EAAE;QAAA,IAAA4C,qBAAA;QACvB,CAAAA,qBAAA,GAAA9C,WAAW,CAACE,OAAO,CAACa,EAAE,CAACgC,YAAY,CAAC,oBAAoB,CAAC,cAAAD,qBAAA,uBAAzDA,qBAAA,CAA2DE,WAAW,CAAC,CAAC;MAC1E;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACErD,OAAA;IACEsD,GAAG,EAAElD,SAAU;IACfF,SAAS,EAAE,aAAaA,SAAS,EAAG;IACpCqD,KAAK,EAAE;MACL/B,QAAQ,EAAE,UAAU;MACpBgC,GAAG,EAAE,CAAC;MACNC,IAAI,EAAE,CAAC;MACP/C,KAAK,EAAE,MAAM;MACbG,MAAM,EAAE,MAAM;MACd6C,aAAa,EAAE,MAAM;MACrBC,MAAM,EAAE;IACV;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEN,CAAC;AAAC5D,EAAA,CA3LIF,QAAQ;AAAA+D,EAAA,GAAR/D,QAAQ;AA6Ld,eAAeA,QAAQ;AAAC,IAAA+D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}