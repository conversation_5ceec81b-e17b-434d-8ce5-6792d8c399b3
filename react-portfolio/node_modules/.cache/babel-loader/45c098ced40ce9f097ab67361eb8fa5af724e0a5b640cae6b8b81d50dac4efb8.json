{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/DarkVeil.js\",\n  _s = $RefreshSig$();\nimport React, { useRef, useEffect } from 'react';\nimport { Renderer, Camera, Transform, Plane, Program, Mesh, Vec2 } from 'ogl';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DarkVeil = ({\n  className = \"\"\n}) => {\n  _s();\n  const canvasRef = useRef();\n  const rendererRef = useRef();\n  const animationRef = useRef();\n  const [webglSupported, setWebglSupported] = React.useState(true);\n  useEffect(() => {\n    if (!canvasRef.current) return;\n    try {\n      // Create renderer\n      const renderer = new Renderer({\n        canvas: canvasRef.current,\n        width: window.innerWidth,\n        height: window.innerHeight,\n        dpr: Math.min(window.devicePixelRatio, 2),\n        alpha: true\n      });\n      rendererRef.current = renderer;\n      const gl = renderer.gl;\n      if (!gl) {\n        setWebglSupported(false);\n        return;\n      }\n      gl.clearColor(0, 0, 0, 0);\n\n      // Create camera\n      const camera = new Camera(gl, {\n        fov: 35\n      });\n      camera.position.set(0, 0, 5);\n\n      // Create scene\n      const scene = new Transform();\n\n      // Vertex shader\n      const vertex = `\n      attribute vec2 uv;\n      attribute vec2 position;\n      uniform mat4 modelViewMatrix;\n      uniform mat4 projectionMatrix;\n      varying vec2 vUv;\n      void main() {\n        vUv = uv;\n        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 0.0, 1.0);\n      }\n    `;\n\n      // Fragment shader\n      const fragment = `\n      precision highp float;\n      uniform float uTime;\n      uniform vec2 uResolution;\n      varying vec2 vUv;\n\n      // Noise function\n      float noise(vec2 p) {\n        return fract(sin(dot(p, vec2(12.9898, 78.233))) * 43758.5453);\n      }\n\n      // Smooth noise\n      float smoothNoise(vec2 p) {\n        vec2 i = floor(p);\n        vec2 f = fract(p);\n        f = f * f * (3.0 - 2.0 * f);\n        \n        float a = noise(i);\n        float b = noise(i + vec2(1.0, 0.0));\n        float c = noise(i + vec2(0.0, 1.0));\n        float d = noise(i + vec2(1.0, 1.0));\n        \n        return mix(mix(a, b, f.x), mix(c, d, f.x), f.y);\n      }\n\n      // Fractal noise\n      float fractalNoise(vec2 p) {\n        float value = 0.0;\n        float amplitude = 0.5;\n        float frequency = 1.0;\n        \n        for(int i = 0; i < 4; i++) {\n          value += amplitude * smoothNoise(p * frequency);\n          amplitude *= 0.5;\n          frequency *= 2.0;\n        }\n        \n        return value;\n      }\n\n      void main() {\n        vec2 uv = vUv;\n        vec2 p = uv * 2.0 - 1.0;\n        p.x *= uResolution.x / uResolution.y;\n        \n        // Create moving noise\n        vec2 noiseCoord = p * 3.0 + uTime * 0.1;\n        float n1 = fractalNoise(noiseCoord);\n        float n2 = fractalNoise(noiseCoord + vec2(100.0));\n        \n        // Create veil effect\n        float veil = smoothstep(0.0, 1.0, length(p) * 0.8);\n        veil = pow(veil, 2.0);\n        \n        // Combine noise with veil\n        float finalNoise = mix(n1, n2, sin(uTime * 0.5) * 0.5 + 0.5);\n        finalNoise = smoothstep(0.3, 0.7, finalNoise);\n        \n        // Create dark veil with subtle variations\n        float darkness = 1.0 - veil * 0.3;\n        darkness *= (1.0 - finalNoise * 0.1);\n        \n        // Add some color variation\n        vec3 color = vec3(0.05, 0.08, 0.12) * darkness;\n        color += vec3(0.02, 0.03, 0.05) * finalNoise * (1.0 - veil);\n        \n        gl_FragColor = vec4(color, darkness * 0.9);\n      }\n    `;\n\n      // Create geometry\n      const geometry = new Plane(gl, {\n        width: 2,\n        height: 2\n      });\n\n      // Create program with error handling\n      let program;\n      try {\n        program = new Program(gl, {\n          vertex,\n          fragment,\n          uniforms: {\n            uTime: {\n              value: 0\n            },\n            uResolution: {\n              value: new Vec2(window.innerWidth, window.innerHeight)\n            }\n          },\n          transparent: true\n        });\n      } catch (error) {\n        console.error('Failed to create WebGL program:', error);\n        setWebglSupported(false);\n        return;\n      }\n    } catch (error) {\n      console.error('WebGL initialization failed:', error);\n      setWebglSupported(false);\n      return;\n    }\n\n    // Create mesh\n    const mesh = new Mesh(gl, {\n      geometry,\n      program\n    });\n    mesh.setParent(scene);\n\n    // Animation loop\n    const animate = time => {\n      program.uniforms.uTime.value = time * 0.001;\n      renderer.render({\n        scene,\n        camera\n      });\n      animationRef.current = requestAnimationFrame(animate);\n    };\n    animate(0);\n\n    // Handle resize\n    const handleResize = () => {\n      const width = window.innerWidth;\n      const height = window.innerHeight;\n      renderer.setSize(width, height);\n      camera.perspective({\n        aspect: width / height\n      });\n      program.uniforms.uResolution.value.set(width, height);\n    };\n    window.addEventListener('resize', handleResize);\n    return () => {\n      window.removeEventListener('resize', handleResize);\n      if (animationRef.current) {\n        cancelAnimationFrame(animationRef.current);\n      }\n      if (rendererRef.current) {\n        var _rendererRef$current$;\n        (_rendererRef$current$ = rendererRef.current.gl.getExtension('WEBGL_lose_context')) === null || _rendererRef$current$ === void 0 ? void 0 : _rendererRef$current$.loseContext();\n      }\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"canvas\", {\n    ref: canvasRef,\n    className: `dark-veil ${className}`,\n    style: {\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      width: '100%',\n      height: '100%',\n      pointerEvents: 'none',\n      zIndex: 0\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 190,\n    columnNumber: 5\n  }, this);\n};\n_s(DarkVeil, \"uURf90rl/gMuOn4QK3rHtVCmo8k=\");\n_c = DarkVeil;\nexport default DarkVeil;\nvar _c;\n$RefreshReg$(_c, \"DarkVeil\");", "map": {"version": 3, "names": ["React", "useRef", "useEffect", "<PERSON><PERSON><PERSON>", "Camera", "Transform", "Plane", "Program", "<PERSON><PERSON>", "Vec2", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON>", "className", "_s", "canvasRef", "rendererRef", "animationRef", "webglSupported", "setWebglSupported", "useState", "current", "renderer", "canvas", "width", "window", "innerWidth", "height", "innerHeight", "dpr", "Math", "min", "devicePixelRatio", "alpha", "gl", "clearColor", "camera", "fov", "position", "set", "scene", "vertex", "fragment", "geometry", "program", "uniforms", "uTime", "value", "uResolution", "transparent", "error", "console", "mesh", "setParent", "animate", "time", "render", "requestAnimationFrame", "handleResize", "setSize", "perspective", "aspect", "addEventListener", "removeEventListener", "cancelAnimationFrame", "_rendererRef$current$", "getExtension", "loseContext", "ref", "style", "top", "left", "pointerEvents", "zIndex", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/DarkVeil.js"], "sourcesContent": ["import React, { useRef, useEffect } from 'react';\nimport { Renderer, Camera, Transform, Plane, Program, Mesh, Vec2 } from 'ogl';\n\nconst DarkVeil = ({ className = \"\" }) => {\n  const canvasRef = useRef();\n  const rendererRef = useRef();\n  const animationRef = useRef();\n  const [webglSupported, setWebglSupported] = React.useState(true);\n\n  useEffect(() => {\n    if (!canvasRef.current) return;\n\n    try {\n      // Create renderer\n      const renderer = new Renderer({\n        canvas: canvasRef.current,\n        width: window.innerWidth,\n        height: window.innerHeight,\n        dpr: Math.min(window.devicePixelRatio, 2),\n        alpha: true,\n      });\n      rendererRef.current = renderer;\n\n      const gl = renderer.gl;\n      if (!gl) {\n        setWebglSupported(false);\n        return;\n      }\n      gl.clearColor(0, 0, 0, 0);\n\n    // Create camera\n    const camera = new Camera(gl, { fov: 35 });\n    camera.position.set(0, 0, 5);\n\n    // Create scene\n    const scene = new Transform();\n\n    // Vertex shader\n    const vertex = `\n      attribute vec2 uv;\n      attribute vec2 position;\n      uniform mat4 modelViewMatrix;\n      uniform mat4 projectionMatrix;\n      varying vec2 vUv;\n      void main() {\n        vUv = uv;\n        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 0.0, 1.0);\n      }\n    `;\n\n    // Fragment shader\n    const fragment = `\n      precision highp float;\n      uniform float uTime;\n      uniform vec2 uResolution;\n      varying vec2 vUv;\n\n      // Noise function\n      float noise(vec2 p) {\n        return fract(sin(dot(p, vec2(12.9898, 78.233))) * 43758.5453);\n      }\n\n      // Smooth noise\n      float smoothNoise(vec2 p) {\n        vec2 i = floor(p);\n        vec2 f = fract(p);\n        f = f * f * (3.0 - 2.0 * f);\n        \n        float a = noise(i);\n        float b = noise(i + vec2(1.0, 0.0));\n        float c = noise(i + vec2(0.0, 1.0));\n        float d = noise(i + vec2(1.0, 1.0));\n        \n        return mix(mix(a, b, f.x), mix(c, d, f.x), f.y);\n      }\n\n      // Fractal noise\n      float fractalNoise(vec2 p) {\n        float value = 0.0;\n        float amplitude = 0.5;\n        float frequency = 1.0;\n        \n        for(int i = 0; i < 4; i++) {\n          value += amplitude * smoothNoise(p * frequency);\n          amplitude *= 0.5;\n          frequency *= 2.0;\n        }\n        \n        return value;\n      }\n\n      void main() {\n        vec2 uv = vUv;\n        vec2 p = uv * 2.0 - 1.0;\n        p.x *= uResolution.x / uResolution.y;\n        \n        // Create moving noise\n        vec2 noiseCoord = p * 3.0 + uTime * 0.1;\n        float n1 = fractalNoise(noiseCoord);\n        float n2 = fractalNoise(noiseCoord + vec2(100.0));\n        \n        // Create veil effect\n        float veil = smoothstep(0.0, 1.0, length(p) * 0.8);\n        veil = pow(veil, 2.0);\n        \n        // Combine noise with veil\n        float finalNoise = mix(n1, n2, sin(uTime * 0.5) * 0.5 + 0.5);\n        finalNoise = smoothstep(0.3, 0.7, finalNoise);\n        \n        // Create dark veil with subtle variations\n        float darkness = 1.0 - veil * 0.3;\n        darkness *= (1.0 - finalNoise * 0.1);\n        \n        // Add some color variation\n        vec3 color = vec3(0.05, 0.08, 0.12) * darkness;\n        color += vec3(0.02, 0.03, 0.05) * finalNoise * (1.0 - veil);\n        \n        gl_FragColor = vec4(color, darkness * 0.9);\n      }\n    `;\n\n    // Create geometry\n    const geometry = new Plane(gl, {\n      width: 2,\n      height: 2,\n    });\n\n    // Create program with error handling\n    let program;\n    try {\n      program = new Program(gl, {\n        vertex,\n        fragment,\n        uniforms: {\n          uTime: { value: 0 },\n          uResolution: { value: new Vec2(window.innerWidth, window.innerHeight) },\n        },\n        transparent: true,\n      });\n    } catch (error) {\n      console.error('Failed to create WebGL program:', error);\n      setWebglSupported(false);\n      return;\n    }\n\n    } catch (error) {\n      console.error('WebGL initialization failed:', error);\n      setWebglSupported(false);\n      return;\n    }\n\n    // Create mesh\n    const mesh = new Mesh(gl, { geometry, program });\n    mesh.setParent(scene);\n\n    // Animation loop\n    const animate = (time) => {\n      program.uniforms.uTime.value = time * 0.001;\n      \n      renderer.render({ scene, camera });\n      animationRef.current = requestAnimationFrame(animate);\n    };\n\n    animate(0);\n\n    // Handle resize\n    const handleResize = () => {\n      const width = window.innerWidth;\n      const height = window.innerHeight;\n      \n      renderer.setSize(width, height);\n      camera.perspective({ aspect: width / height });\n      program.uniforms.uResolution.value.set(width, height);\n    };\n\n    window.addEventListener('resize', handleResize);\n\n    return () => {\n      window.removeEventListener('resize', handleResize);\n      if (animationRef.current) {\n        cancelAnimationFrame(animationRef.current);\n      }\n      if (rendererRef.current) {\n        rendererRef.current.gl.getExtension('WEBGL_lose_context')?.loseContext();\n      }\n    };\n  }, []);\n\n  return (\n    <canvas\n      ref={canvasRef}\n      className={`dark-veil ${className}`}\n      style={{\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        width: '100%',\n        height: '100%',\n        pointerEvents: 'none',\n        zIndex: 0,\n      }}\n    />\n  );\n};\n\nexport default DarkVeil;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAChD,SAASC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,KAAK,EAAEC,OAAO,EAAEC,IAAI,EAAEC,IAAI,QAAQ,KAAK;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9E,MAAMC,QAAQ,GAAGA,CAAC;EAAEC,SAAS,GAAG;AAAG,CAAC,KAAK;EAAAC,EAAA;EACvC,MAAMC,SAAS,GAAGd,MAAM,CAAC,CAAC;EAC1B,MAAMe,WAAW,GAAGf,MAAM,CAAC,CAAC;EAC5B,MAAMgB,YAAY,GAAGhB,MAAM,CAAC,CAAC;EAC7B,MAAM,CAACiB,cAAc,EAAEC,iBAAiB,CAAC,GAAGnB,KAAK,CAACoB,QAAQ,CAAC,IAAI,CAAC;EAEhElB,SAAS,CAAC,MAAM;IACd,IAAI,CAACa,SAAS,CAACM,OAAO,EAAE;IAExB,IAAI;MACF;MACA,MAAMC,QAAQ,GAAG,IAAInB,QAAQ,CAAC;QAC5BoB,MAAM,EAAER,SAAS,CAACM,OAAO;QACzBG,KAAK,EAAEC,MAAM,CAACC,UAAU;QACxBC,MAAM,EAAEF,MAAM,CAACG,WAAW;QAC1BC,GAAG,EAAEC,IAAI,CAACC,GAAG,CAACN,MAAM,CAACO,gBAAgB,EAAE,CAAC,CAAC;QACzCC,KAAK,EAAE;MACT,CAAC,CAAC;MACFjB,WAAW,CAACK,OAAO,GAAGC,QAAQ;MAE9B,MAAMY,EAAE,GAAGZ,QAAQ,CAACY,EAAE;MACtB,IAAI,CAACA,EAAE,EAAE;QACPf,iBAAiB,CAAC,KAAK,CAAC;QACxB;MACF;MACAe,EAAE,CAACC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;MAE3B;MACA,MAAMC,MAAM,GAAG,IAAIhC,MAAM,CAAC8B,EAAE,EAAE;QAAEG,GAAG,EAAE;MAAG,CAAC,CAAC;MAC1CD,MAAM,CAACE,QAAQ,CAACC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;MAE5B;MACA,MAAMC,KAAK,GAAG,IAAInC,SAAS,CAAC,CAAC;;MAE7B;MACA,MAAMoC,MAAM,GAAG;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;;MAED;MACA,MAAMC,QAAQ,GAAG;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;;MAED;MACA,MAAMC,QAAQ,GAAG,IAAIrC,KAAK,CAAC4B,EAAE,EAAE;QAC7BV,KAAK,EAAE,CAAC;QACRG,MAAM,EAAE;MACV,CAAC,CAAC;;MAEF;MACA,IAAIiB,OAAO;MACX,IAAI;QACFA,OAAO,GAAG,IAAIrC,OAAO,CAAC2B,EAAE,EAAE;UACxBO,MAAM;UACNC,QAAQ;UACRG,QAAQ,EAAE;YACRC,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAE,CAAC;YACnBC,WAAW,EAAE;cAAED,KAAK,EAAE,IAAItC,IAAI,CAACgB,MAAM,CAACC,UAAU,EAAED,MAAM,CAACG,WAAW;YAAE;UACxE,CAAC;UACDqB,WAAW,EAAE;QACf,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACvD/B,iBAAiB,CAAC,KAAK,CAAC;QACxB;MACF;IAEA,CAAC,CAAC,OAAO+B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD/B,iBAAiB,CAAC,KAAK,CAAC;MACxB;IACF;;IAEA;IACA,MAAMiC,IAAI,GAAG,IAAI5C,IAAI,CAAC0B,EAAE,EAAE;MAAES,QAAQ;MAAEC;IAAQ,CAAC,CAAC;IAChDQ,IAAI,CAACC,SAAS,CAACb,KAAK,CAAC;;IAErB;IACA,MAAMc,OAAO,GAAIC,IAAI,IAAK;MACxBX,OAAO,CAACC,QAAQ,CAACC,KAAK,CAACC,KAAK,GAAGQ,IAAI,GAAG,KAAK;MAE3CjC,QAAQ,CAACkC,MAAM,CAAC;QAAEhB,KAAK;QAAEJ;MAAO,CAAC,CAAC;MAClCnB,YAAY,CAACI,OAAO,GAAGoC,qBAAqB,CAACH,OAAO,CAAC;IACvD,CAAC;IAEDA,OAAO,CAAC,CAAC,CAAC;;IAEV;IACA,MAAMI,YAAY,GAAGA,CAAA,KAAM;MACzB,MAAMlC,KAAK,GAAGC,MAAM,CAACC,UAAU;MAC/B,MAAMC,MAAM,GAAGF,MAAM,CAACG,WAAW;MAEjCN,QAAQ,CAACqC,OAAO,CAACnC,KAAK,EAAEG,MAAM,CAAC;MAC/BS,MAAM,CAACwB,WAAW,CAAC;QAAEC,MAAM,EAAErC,KAAK,GAAGG;MAAO,CAAC,CAAC;MAC9CiB,OAAO,CAACC,QAAQ,CAACG,WAAW,CAACD,KAAK,CAACR,GAAG,CAACf,KAAK,EAAEG,MAAM,CAAC;IACvD,CAAC;IAEDF,MAAM,CAACqC,gBAAgB,CAAC,QAAQ,EAAEJ,YAAY,CAAC;IAE/C,OAAO,MAAM;MACXjC,MAAM,CAACsC,mBAAmB,CAAC,QAAQ,EAAEL,YAAY,CAAC;MAClD,IAAIzC,YAAY,CAACI,OAAO,EAAE;QACxB2C,oBAAoB,CAAC/C,YAAY,CAACI,OAAO,CAAC;MAC5C;MACA,IAAIL,WAAW,CAACK,OAAO,EAAE;QAAA,IAAA4C,qBAAA;QACvB,CAAAA,qBAAA,GAAAjD,WAAW,CAACK,OAAO,CAACa,EAAE,CAACgC,YAAY,CAAC,oBAAoB,CAAC,cAAAD,qBAAA,uBAAzDA,qBAAA,CAA2DE,WAAW,CAAC,CAAC;MAC1E;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACExD,OAAA;IACEyD,GAAG,EAAErD,SAAU;IACfF,SAAS,EAAE,aAAaA,SAAS,EAAG;IACpCwD,KAAK,EAAE;MACL/B,QAAQ,EAAE,UAAU;MACpBgC,GAAG,EAAE,CAAC;MACNC,IAAI,EAAE,CAAC;MACP/C,KAAK,EAAE,MAAM;MACbG,MAAM,EAAE,MAAM;MACd6C,aAAa,EAAE,MAAM;MACrBC,MAAM,EAAE;IACV;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEN,CAAC;AAAC/D,EAAA,CAxMIF,QAAQ;AAAAkE,EAAA,GAARlE,QAAQ;AA0Md,eAAeA,QAAQ;AAAC,IAAAkE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}