{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/DarkVeil.js\",\n  _s = $RefreshSig$();\nimport React, { useRef, useEffect, useState } from 'react';\nimport './DarkVeil.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DarkVeil = ({\n  className = \"\"\n}) => {\n  _s();\n  const canvasRef = useRef(null);\n  const animationRef = useRef(null);\n  const [dimensions, setDimensions] = useState({\n    width: 0,\n    height: 0\n  });\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n    const ctx = canvas.getContext('2d');\n    if (!ctx) return;\n\n    // Set canvas size\n    const updateSize = () => {\n      const rect = canvas.getBoundingClientRect();\n      const dpr = window.devicePixelRatio || 1;\n      const width = Math.max(rect.width, 1);\n      const height = Math.max(rect.height, 1);\n      canvas.width = width * dpr;\n      canvas.height = height * dpr;\n      ctx.scale(dpr, dpr);\n      canvas.style.width = width + 'px';\n      canvas.style.height = height + 'px';\n      setDimensions({\n        width,\n        height\n      });\n    };\n    updateSize();\n    window.addEventListener('resize', updateSize);\n\n    // Veil particles\n    const particles = [];\n    const particleCount = 150;\n\n    // Initialize particles\n    const initParticles = () => {\n      particles.length = 0; // Clear existing particles\n      const width = Math.max(dimensions.width, 100);\n      const height = Math.max(dimensions.height, 100);\n      for (let i = 0; i < particleCount; i++) {\n        particles.push({\n          x: Math.random() * width,\n          y: Math.random() * height,\n          size: Math.random() * 2 + 0.5,\n          speedX: (Math.random() - 0.5) * 0.5,\n          speedY: (Math.random() - 0.5) * 0.5,\n          opacity: Math.random() * 0.3 + 0.1,\n          life: Math.random() * 100,\n          maxLife: Math.random() * 100 + 50\n        });\n      }\n    };\n    initParticles();\n\n    // Animation loop\n    let time = 0;\n    const animate = () => {\n      if (dimensions.width <= 0 || dimensions.height <= 0) {\n        animationRef.current = requestAnimationFrame(animate);\n        return;\n      }\n      time += 0.01;\n\n      // Clear canvas with dark background\n      ctx.fillStyle = 'rgba(0, 0, 0, 0.02)';\n      ctx.fillRect(0, 0, dimensions.width, dimensions.height);\n\n      // Create veil effect with gradients\n      const gradient1 = ctx.createRadialGradient(dimensions.width * 0.3, dimensions.height * 0.3, 0, dimensions.width * 0.3, dimensions.height * 0.3, dimensions.width * 0.8);\n      gradient1.addColorStop(0, 'rgba(5, 8, 12, 0.9)');\n      gradient1.addColorStop(0.3, 'rgba(10, 15, 20, 0.7)');\n      gradient1.addColorStop(0.7, 'rgba(0, 0, 0, 0.95)');\n      gradient1.addColorStop(1, 'transparent');\n      ctx.fillStyle = gradient1;\n      ctx.fillRect(0, 0, dimensions.width, dimensions.height);\n\n      // Second gradient layer\n      const gradient2 = ctx.createRadialGradient(dimensions.width * 0.7 + Math.sin(time) * 50, dimensions.height * 0.7 + Math.cos(time * 0.8) * 30, 0, dimensions.width * 0.7, dimensions.height * 0.7, dimensions.width * 0.6);\n      gradient2.addColorStop(0, 'rgba(8, 12, 18, 0.8)');\n      gradient2.addColorStop(0.4, 'rgba(15, 20, 25, 0.6)');\n      gradient2.addColorStop(0.8, 'rgba(0, 0, 0, 0.9)');\n      gradient2.addColorStop(1, 'transparent');\n      ctx.globalCompositeOperation = 'multiply';\n      ctx.fillStyle = gradient2;\n      ctx.fillRect(0, 0, dimensions.width, dimensions.height);\n      ctx.globalCompositeOperation = 'source-over';\n\n      // Draw and update particles\n      particles.forEach((particle, index) => {\n        // Update particle\n        particle.x += particle.speedX + Math.sin(time + index * 0.1) * 0.1;\n        particle.y += particle.speedY + Math.cos(time + index * 0.1) * 0.1;\n        particle.life++;\n\n        // Wrap around screen\n        if (particle.x < 0) particle.x = dimensions.width;\n        if (particle.x > dimensions.width) particle.x = 0;\n        if (particle.y < 0) particle.y = dimensions.height;\n        if (particle.y > dimensions.height) particle.y = 0;\n\n        // Reset particle if life exceeded\n        if (particle.life > particle.maxLife) {\n          particle.x = Math.random() * Math.max(dimensions.width, 100);\n          particle.y = Math.random() * Math.max(dimensions.height, 100);\n          particle.life = 0;\n          particle.opacity = Math.random() * 0.3 + 0.1;\n        }\n\n        // Draw particle\n        const alpha = particle.opacity * (1 - particle.life / particle.maxLife);\n        ctx.fillStyle = `rgba(255, 255, 255, ${alpha * 0.1})`;\n        ctx.beginPath();\n        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);\n        ctx.fill();\n      });\n\n      // Add noise texture (safer approach)\n      if (dimensions.width > 0 && dimensions.height > 0) {\n        try {\n          // Create noise with small dots instead of imageData manipulation\n          ctx.fillStyle = 'rgba(255, 255, 255, 0.02)';\n          for (let i = 0; i < 50; i++) {\n            const x = Math.random() * dimensions.width;\n            const y = Math.random() * dimensions.height;\n            const size = Math.random() * 1;\n            ctx.beginPath();\n            ctx.arc(x, y, size, 0, Math.PI * 2);\n            ctx.fill();\n          }\n        } catch (error) {\n          // Fallback: skip noise if there's an error\n          console.warn('Noise texture skipped:', error);\n        }\n      }\n      animationRef.current = requestAnimationFrame(animate);\n    };\n    animate();\n    return () => {\n      window.removeEventListener('resize', updateSize);\n      if (animationRef.current) {\n        cancelAnimationFrame(animationRef.current);\n      }\n    };\n  }, [dimensions.width, dimensions.height]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `dark-veil-container ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(\"canvas\", {\n      ref: canvasRef,\n      className: \"dark-veil-canvas\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 176,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dark-veil-overlay\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 175,\n    columnNumber: 5\n  }, this);\n};\n_s(DarkVeil, \"wmQfh6sk44RhTBcfaDTp0svxuQ8=\");\n_c = DarkVeil;\nexport default DarkVeil;\nvar _c;\n$RefreshReg$(_c, \"DarkVeil\");", "map": {"version": 3, "names": ["React", "useRef", "useEffect", "useState", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON>", "className", "_s", "canvasRef", "animationRef", "dimensions", "setDimensions", "width", "height", "canvas", "current", "ctx", "getContext", "updateSize", "rect", "getBoundingClientRect", "dpr", "window", "devicePixelRatio", "Math", "max", "scale", "style", "addEventListener", "particles", "particleCount", "initParticles", "length", "i", "push", "x", "random", "y", "size", "speedX", "speedY", "opacity", "life", "maxLife", "time", "animate", "requestAnimationFrame", "fillStyle", "fillRect", "gradient1", "createRadialGradient", "addColorStop", "gradient2", "sin", "cos", "globalCompositeOperation", "for<PERSON>ach", "particle", "index", "alpha", "beginPath", "arc", "PI", "fill", "error", "console", "warn", "removeEventListener", "cancelAnimationFrame", "children", "ref", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/DarkVeil.js"], "sourcesContent": ["import React, { useRef, useEffect, useState } from 'react';\nimport './DarkVeil.css';\n\nconst DarkVeil = ({ className = \"\" }) => {\n  const canvasRef = useRef(null);\n  const animationRef = useRef(null);\n  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });\n\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n\n    const ctx = canvas.getContext('2d');\n    if (!ctx) return;\n\n    // Set canvas size\n    const updateSize = () => {\n      const rect = canvas.getBoundingClientRect();\n      const dpr = window.devicePixelRatio || 1;\n\n      const width = Math.max(rect.width, 1);\n      const height = Math.max(rect.height, 1);\n\n      canvas.width = width * dpr;\n      canvas.height = height * dpr;\n\n      ctx.scale(dpr, dpr);\n      canvas.style.width = width + 'px';\n      canvas.style.height = height + 'px';\n\n      setDimensions({ width, height });\n    };\n\n    updateSize();\n    window.addEventListener('resize', updateSize);\n\n    // Veil particles\n    const particles = [];\n    const particleCount = 150;\n\n    // Initialize particles\n    const initParticles = () => {\n      particles.length = 0; // Clear existing particles\n      const width = Math.max(dimensions.width, 100);\n      const height = Math.max(dimensions.height, 100);\n\n      for (let i = 0; i < particleCount; i++) {\n        particles.push({\n          x: Math.random() * width,\n          y: Math.random() * height,\n          size: Math.random() * 2 + 0.5,\n          speedX: (Math.random() - 0.5) * 0.5,\n          speedY: (Math.random() - 0.5) * 0.5,\n          opacity: Math.random() * 0.3 + 0.1,\n          life: Math.random() * 100,\n          maxLife: Math.random() * 100 + 50\n        });\n      }\n    };\n\n    initParticles();\n\n    // Animation loop\n    let time = 0;\n    const animate = () => {\n      if (dimensions.width <= 0 || dimensions.height <= 0) {\n        animationRef.current = requestAnimationFrame(animate);\n        return;\n      }\n\n      time += 0.01;\n\n      // Clear canvas with dark background\n      ctx.fillStyle = 'rgba(0, 0, 0, 0.02)';\n      ctx.fillRect(0, 0, dimensions.width, dimensions.height);\n\n      // Create veil effect with gradients\n      const gradient1 = ctx.createRadialGradient(\n        dimensions.width * 0.3, \n        dimensions.height * 0.3, \n        0,\n        dimensions.width * 0.3, \n        dimensions.height * 0.3, \n        dimensions.width * 0.8\n      );\n      gradient1.addColorStop(0, 'rgba(5, 8, 12, 0.9)');\n      gradient1.addColorStop(0.3, 'rgba(10, 15, 20, 0.7)');\n      gradient1.addColorStop(0.7, 'rgba(0, 0, 0, 0.95)');\n      gradient1.addColorStop(1, 'transparent');\n\n      ctx.fillStyle = gradient1;\n      ctx.fillRect(0, 0, dimensions.width, dimensions.height);\n\n      // Second gradient layer\n      const gradient2 = ctx.createRadialGradient(\n        dimensions.width * 0.7 + Math.sin(time) * 50, \n        dimensions.height * 0.7 + Math.cos(time * 0.8) * 30, \n        0,\n        dimensions.width * 0.7, \n        dimensions.height * 0.7, \n        dimensions.width * 0.6\n      );\n      gradient2.addColorStop(0, 'rgba(8, 12, 18, 0.8)');\n      gradient2.addColorStop(0.4, 'rgba(15, 20, 25, 0.6)');\n      gradient2.addColorStop(0.8, 'rgba(0, 0, 0, 0.9)');\n      gradient2.addColorStop(1, 'transparent');\n\n      ctx.globalCompositeOperation = 'multiply';\n      ctx.fillStyle = gradient2;\n      ctx.fillRect(0, 0, dimensions.width, dimensions.height);\n      ctx.globalCompositeOperation = 'source-over';\n\n      // Draw and update particles\n      particles.forEach((particle, index) => {\n        // Update particle\n        particle.x += particle.speedX + Math.sin(time + index * 0.1) * 0.1;\n        particle.y += particle.speedY + Math.cos(time + index * 0.1) * 0.1;\n        particle.life++;\n\n        // Wrap around screen\n        if (particle.x < 0) particle.x = dimensions.width;\n        if (particle.x > dimensions.width) particle.x = 0;\n        if (particle.y < 0) particle.y = dimensions.height;\n        if (particle.y > dimensions.height) particle.y = 0;\n\n        // Reset particle if life exceeded\n        if (particle.life > particle.maxLife) {\n          particle.x = Math.random() * Math.max(dimensions.width, 100);\n          particle.y = Math.random() * Math.max(dimensions.height, 100);\n          particle.life = 0;\n          particle.opacity = Math.random() * 0.3 + 0.1;\n        }\n\n        // Draw particle\n        const alpha = particle.opacity * (1 - particle.life / particle.maxLife);\n        ctx.fillStyle = `rgba(255, 255, 255, ${alpha * 0.1})`;\n        ctx.beginPath();\n        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);\n        ctx.fill();\n      });\n\n      // Add noise texture (safer approach)\n      if (dimensions.width > 0 && dimensions.height > 0) {\n        try {\n          // Create noise with small dots instead of imageData manipulation\n          ctx.fillStyle = 'rgba(255, 255, 255, 0.02)';\n          for (let i = 0; i < 50; i++) {\n            const x = Math.random() * dimensions.width;\n            const y = Math.random() * dimensions.height;\n            const size = Math.random() * 1;\n            ctx.beginPath();\n            ctx.arc(x, y, size, 0, Math.PI * 2);\n            ctx.fill();\n          }\n        } catch (error) {\n          // Fallback: skip noise if there's an error\n          console.warn('Noise texture skipped:', error);\n        }\n      }\n\n      animationRef.current = requestAnimationFrame(animate);\n    };\n\n    animate();\n\n    return () => {\n      window.removeEventListener('resize', updateSize);\n      if (animationRef.current) {\n        cancelAnimationFrame(animationRef.current);\n      }\n    };\n  }, [dimensions.width, dimensions.height]);\n\n  return (\n    <div className={`dark-veil-container ${className}`}>\n      <canvas\n        ref={canvasRef}\n        className=\"dark-veil-canvas\"\n      />\n      <div className=\"dark-veil-overlay\" />\n    </div>\n  );\n};\n\nexport default DarkVeil;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,OAAO,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,QAAQ,GAAGA,CAAC;EAAEC,SAAS,GAAG;AAAG,CAAC,KAAK;EAAAC,EAAA;EACvC,MAAMC,SAAS,GAAGR,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMS,YAAY,GAAGT,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM,CAACU,UAAU,EAAEC,aAAa,CAAC,GAAGT,QAAQ,CAAC;IAAEU,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE,CAAC,CAAC;EAErEZ,SAAS,CAAC,MAAM;IACd,MAAMa,MAAM,GAAGN,SAAS,CAACO,OAAO;IAChC,IAAI,CAACD,MAAM,EAAE;IAEb,MAAME,GAAG,GAAGF,MAAM,CAACG,UAAU,CAAC,IAAI,CAAC;IACnC,IAAI,CAACD,GAAG,EAAE;;IAEV;IACA,MAAME,UAAU,GAAGA,CAAA,KAAM;MACvB,MAAMC,IAAI,GAAGL,MAAM,CAACM,qBAAqB,CAAC,CAAC;MAC3C,MAAMC,GAAG,GAAGC,MAAM,CAACC,gBAAgB,IAAI,CAAC;MAExC,MAAMX,KAAK,GAAGY,IAAI,CAACC,GAAG,CAACN,IAAI,CAACP,KAAK,EAAE,CAAC,CAAC;MACrC,MAAMC,MAAM,GAAGW,IAAI,CAACC,GAAG,CAACN,IAAI,CAACN,MAAM,EAAE,CAAC,CAAC;MAEvCC,MAAM,CAACF,KAAK,GAAGA,KAAK,GAAGS,GAAG;MAC1BP,MAAM,CAACD,MAAM,GAAGA,MAAM,GAAGQ,GAAG;MAE5BL,GAAG,CAACU,KAAK,CAACL,GAAG,EAAEA,GAAG,CAAC;MACnBP,MAAM,CAACa,KAAK,CAACf,KAAK,GAAGA,KAAK,GAAG,IAAI;MACjCE,MAAM,CAACa,KAAK,CAACd,MAAM,GAAGA,MAAM,GAAG,IAAI;MAEnCF,aAAa,CAAC;QAAEC,KAAK;QAAEC;MAAO,CAAC,CAAC;IAClC,CAAC;IAEDK,UAAU,CAAC,CAAC;IACZI,MAAM,CAACM,gBAAgB,CAAC,QAAQ,EAAEV,UAAU,CAAC;;IAE7C;IACA,MAAMW,SAAS,GAAG,EAAE;IACpB,MAAMC,aAAa,GAAG,GAAG;;IAEzB;IACA,MAAMC,aAAa,GAAGA,CAAA,KAAM;MAC1BF,SAAS,CAACG,MAAM,GAAG,CAAC,CAAC,CAAC;MACtB,MAAMpB,KAAK,GAAGY,IAAI,CAACC,GAAG,CAACf,UAAU,CAACE,KAAK,EAAE,GAAG,CAAC;MAC7C,MAAMC,MAAM,GAAGW,IAAI,CAACC,GAAG,CAACf,UAAU,CAACG,MAAM,EAAE,GAAG,CAAC;MAE/C,KAAK,IAAIoB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,aAAa,EAAEG,CAAC,EAAE,EAAE;QACtCJ,SAAS,CAACK,IAAI,CAAC;UACbC,CAAC,EAAEX,IAAI,CAACY,MAAM,CAAC,CAAC,GAAGxB,KAAK;UACxByB,CAAC,EAAEb,IAAI,CAACY,MAAM,CAAC,CAAC,GAAGvB,MAAM;UACzByB,IAAI,EAAEd,IAAI,CAACY,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG;UAC7BG,MAAM,EAAE,CAACf,IAAI,CAACY,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG;UACnCI,MAAM,EAAE,CAAChB,IAAI,CAACY,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG;UACnCK,OAAO,EAAEjB,IAAI,CAACY,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;UAClCM,IAAI,EAAElB,IAAI,CAACY,MAAM,CAAC,CAAC,GAAG,GAAG;UACzBO,OAAO,EAAEnB,IAAI,CAACY,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;QACjC,CAAC,CAAC;MACJ;IACF,CAAC;IAEDL,aAAa,CAAC,CAAC;;IAEf;IACA,IAAIa,IAAI,GAAG,CAAC;IACZ,MAAMC,OAAO,GAAGA,CAAA,KAAM;MACpB,IAAInC,UAAU,CAACE,KAAK,IAAI,CAAC,IAAIF,UAAU,CAACG,MAAM,IAAI,CAAC,EAAE;QACnDJ,YAAY,CAACM,OAAO,GAAG+B,qBAAqB,CAACD,OAAO,CAAC;QACrD;MACF;MAEAD,IAAI,IAAI,IAAI;;MAEZ;MACA5B,GAAG,CAAC+B,SAAS,GAAG,qBAAqB;MACrC/B,GAAG,CAACgC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAEtC,UAAU,CAACE,KAAK,EAAEF,UAAU,CAACG,MAAM,CAAC;;MAEvD;MACA,MAAMoC,SAAS,GAAGjC,GAAG,CAACkC,oBAAoB,CACxCxC,UAAU,CAACE,KAAK,GAAG,GAAG,EACtBF,UAAU,CAACG,MAAM,GAAG,GAAG,EACvB,CAAC,EACDH,UAAU,CAACE,KAAK,GAAG,GAAG,EACtBF,UAAU,CAACG,MAAM,GAAG,GAAG,EACvBH,UAAU,CAACE,KAAK,GAAG,GACrB,CAAC;MACDqC,SAAS,CAACE,YAAY,CAAC,CAAC,EAAE,qBAAqB,CAAC;MAChDF,SAAS,CAACE,YAAY,CAAC,GAAG,EAAE,uBAAuB,CAAC;MACpDF,SAAS,CAACE,YAAY,CAAC,GAAG,EAAE,qBAAqB,CAAC;MAClDF,SAAS,CAACE,YAAY,CAAC,CAAC,EAAE,aAAa,CAAC;MAExCnC,GAAG,CAAC+B,SAAS,GAAGE,SAAS;MACzBjC,GAAG,CAACgC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAEtC,UAAU,CAACE,KAAK,EAAEF,UAAU,CAACG,MAAM,CAAC;;MAEvD;MACA,MAAMuC,SAAS,GAAGpC,GAAG,CAACkC,oBAAoB,CACxCxC,UAAU,CAACE,KAAK,GAAG,GAAG,GAAGY,IAAI,CAAC6B,GAAG,CAACT,IAAI,CAAC,GAAG,EAAE,EAC5ClC,UAAU,CAACG,MAAM,GAAG,GAAG,GAAGW,IAAI,CAAC8B,GAAG,CAACV,IAAI,GAAG,GAAG,CAAC,GAAG,EAAE,EACnD,CAAC,EACDlC,UAAU,CAACE,KAAK,GAAG,GAAG,EACtBF,UAAU,CAACG,MAAM,GAAG,GAAG,EACvBH,UAAU,CAACE,KAAK,GAAG,GACrB,CAAC;MACDwC,SAAS,CAACD,YAAY,CAAC,CAAC,EAAE,sBAAsB,CAAC;MACjDC,SAAS,CAACD,YAAY,CAAC,GAAG,EAAE,uBAAuB,CAAC;MACpDC,SAAS,CAACD,YAAY,CAAC,GAAG,EAAE,oBAAoB,CAAC;MACjDC,SAAS,CAACD,YAAY,CAAC,CAAC,EAAE,aAAa,CAAC;MAExCnC,GAAG,CAACuC,wBAAwB,GAAG,UAAU;MACzCvC,GAAG,CAAC+B,SAAS,GAAGK,SAAS;MACzBpC,GAAG,CAACgC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAEtC,UAAU,CAACE,KAAK,EAAEF,UAAU,CAACG,MAAM,CAAC;MACvDG,GAAG,CAACuC,wBAAwB,GAAG,aAAa;;MAE5C;MACA1B,SAAS,CAAC2B,OAAO,CAAC,CAACC,QAAQ,EAAEC,KAAK,KAAK;QACrC;QACAD,QAAQ,CAACtB,CAAC,IAAIsB,QAAQ,CAAClB,MAAM,GAAGf,IAAI,CAAC6B,GAAG,CAACT,IAAI,GAAGc,KAAK,GAAG,GAAG,CAAC,GAAG,GAAG;QAClED,QAAQ,CAACpB,CAAC,IAAIoB,QAAQ,CAACjB,MAAM,GAAGhB,IAAI,CAAC8B,GAAG,CAACV,IAAI,GAAGc,KAAK,GAAG,GAAG,CAAC,GAAG,GAAG;QAClED,QAAQ,CAACf,IAAI,EAAE;;QAEf;QACA,IAAIe,QAAQ,CAACtB,CAAC,GAAG,CAAC,EAAEsB,QAAQ,CAACtB,CAAC,GAAGzB,UAAU,CAACE,KAAK;QACjD,IAAI6C,QAAQ,CAACtB,CAAC,GAAGzB,UAAU,CAACE,KAAK,EAAE6C,QAAQ,CAACtB,CAAC,GAAG,CAAC;QACjD,IAAIsB,QAAQ,CAACpB,CAAC,GAAG,CAAC,EAAEoB,QAAQ,CAACpB,CAAC,GAAG3B,UAAU,CAACG,MAAM;QAClD,IAAI4C,QAAQ,CAACpB,CAAC,GAAG3B,UAAU,CAACG,MAAM,EAAE4C,QAAQ,CAACpB,CAAC,GAAG,CAAC;;QAElD;QACA,IAAIoB,QAAQ,CAACf,IAAI,GAAGe,QAAQ,CAACd,OAAO,EAAE;UACpCc,QAAQ,CAACtB,CAAC,GAAGX,IAAI,CAACY,MAAM,CAAC,CAAC,GAAGZ,IAAI,CAACC,GAAG,CAACf,UAAU,CAACE,KAAK,EAAE,GAAG,CAAC;UAC5D6C,QAAQ,CAACpB,CAAC,GAAGb,IAAI,CAACY,MAAM,CAAC,CAAC,GAAGZ,IAAI,CAACC,GAAG,CAACf,UAAU,CAACG,MAAM,EAAE,GAAG,CAAC;UAC7D4C,QAAQ,CAACf,IAAI,GAAG,CAAC;UACjBe,QAAQ,CAAChB,OAAO,GAAGjB,IAAI,CAACY,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;QAC9C;;QAEA;QACA,MAAMuB,KAAK,GAAGF,QAAQ,CAAChB,OAAO,IAAI,CAAC,GAAGgB,QAAQ,CAACf,IAAI,GAAGe,QAAQ,CAACd,OAAO,CAAC;QACvE3B,GAAG,CAAC+B,SAAS,GAAG,uBAAuBY,KAAK,GAAG,GAAG,GAAG;QACrD3C,GAAG,CAAC4C,SAAS,CAAC,CAAC;QACf5C,GAAG,CAAC6C,GAAG,CAACJ,QAAQ,CAACtB,CAAC,EAAEsB,QAAQ,CAACpB,CAAC,EAAEoB,QAAQ,CAACnB,IAAI,EAAE,CAAC,EAAEd,IAAI,CAACsC,EAAE,GAAG,CAAC,CAAC;QAC9D9C,GAAG,CAAC+C,IAAI,CAAC,CAAC;MACZ,CAAC,CAAC;;MAEF;MACA,IAAIrD,UAAU,CAACE,KAAK,GAAG,CAAC,IAAIF,UAAU,CAACG,MAAM,GAAG,CAAC,EAAE;QACjD,IAAI;UACF;UACAG,GAAG,CAAC+B,SAAS,GAAG,2BAA2B;UAC3C,KAAK,IAAId,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;YAC3B,MAAME,CAAC,GAAGX,IAAI,CAACY,MAAM,CAAC,CAAC,GAAG1B,UAAU,CAACE,KAAK;YAC1C,MAAMyB,CAAC,GAAGb,IAAI,CAACY,MAAM,CAAC,CAAC,GAAG1B,UAAU,CAACG,MAAM;YAC3C,MAAMyB,IAAI,GAAGd,IAAI,CAACY,MAAM,CAAC,CAAC,GAAG,CAAC;YAC9BpB,GAAG,CAAC4C,SAAS,CAAC,CAAC;YACf5C,GAAG,CAAC6C,GAAG,CAAC1B,CAAC,EAAEE,CAAC,EAAEC,IAAI,EAAE,CAAC,EAAEd,IAAI,CAACsC,EAAE,GAAG,CAAC,CAAC;YACnC9C,GAAG,CAAC+C,IAAI,CAAC,CAAC;UACZ;QACF,CAAC,CAAC,OAAOC,KAAK,EAAE;UACd;UACAC,OAAO,CAACC,IAAI,CAAC,wBAAwB,EAAEF,KAAK,CAAC;QAC/C;MACF;MAEAvD,YAAY,CAACM,OAAO,GAAG+B,qBAAqB,CAACD,OAAO,CAAC;IACvD,CAAC;IAEDA,OAAO,CAAC,CAAC;IAET,OAAO,MAAM;MACXvB,MAAM,CAAC6C,mBAAmB,CAAC,QAAQ,EAAEjD,UAAU,CAAC;MAChD,IAAIT,YAAY,CAACM,OAAO,EAAE;QACxBqD,oBAAoB,CAAC3D,YAAY,CAACM,OAAO,CAAC;MAC5C;IACF,CAAC;EACH,CAAC,EAAE,CAACL,UAAU,CAACE,KAAK,EAAEF,UAAU,CAACG,MAAM,CAAC,CAAC;EAEzC,oBACET,OAAA;IAAKE,SAAS,EAAE,uBAAuBA,SAAS,EAAG;IAAA+D,QAAA,gBACjDjE,OAAA;MACEkE,GAAG,EAAE9D,SAAU;MACfF,SAAS,EAAC;IAAkB;MAAAiE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CAAC,eACFtE,OAAA;MAAKE,SAAS,EAAC;IAAmB;MAAAiE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAClC,CAAC;AAEV,CAAC;AAACnE,EAAA,CAnLIF,QAAQ;AAAAsE,EAAA,GAARtE,QAAQ;AAqLd,eAAeA,QAAQ;AAAC,IAAAsE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}