{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/DarkVeil.js\",\n  _s = $RefreshSig$();\nimport React, { useRef, useEffect, useState } from 'react';\nimport './DarkVeil.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DarkVeil = ({\n  className = \"\"\n}) => {\n  _s();\n  const canvasRef = useRef(null);\n  const animationRef = useRef(null);\n  const [dimensions, setDimensions] = useState({\n    width: 0,\n    height: 0\n  });\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n    const ctx = canvas.getContext('2d');\n    if (!ctx) return;\n\n    // Set canvas size\n    const updateSize = () => {\n      const rect = canvas.getBoundingClientRect();\n      const dpr = window.devicePixelRatio || 1;\n      canvas.width = rect.width * dpr;\n      canvas.height = rect.height * dpr;\n      ctx.scale(dpr, dpr);\n      canvas.style.width = rect.width + 'px';\n      canvas.style.height = rect.height + 'px';\n      setDimensions({\n        width: rect.width,\n        height: rect.height\n      });\n    };\n    updateSize();\n    window.addEventListener('resize', updateSize);\n\n    // Veil particles\n    const particles = [];\n    const particleCount = 150;\n\n    // Initialize particles\n    for (let i = 0; i < particleCount; i++) {\n      particles.push({\n        x: Math.random() * dimensions.width,\n        y: Math.random() * dimensions.height,\n        size: Math.random() * 2 + 0.5,\n        speedX: (Math.random() - 0.5) * 0.5,\n        speedY: (Math.random() - 0.5) * 0.5,\n        opacity: Math.random() * 0.3 + 0.1,\n        life: Math.random() * 100,\n        maxLife: Math.random() * 100 + 50\n      });\n    }\n\n    // Animation loop\n    let time = 0;\n    const animate = () => {\n      time += 0.01;\n\n      // Clear canvas with dark background\n      ctx.fillStyle = 'rgba(0, 0, 0, 0.02)';\n      ctx.fillRect(0, 0, dimensions.width, dimensions.height);\n\n      // Create veil effect with gradients\n      const gradient1 = ctx.createRadialGradient(dimensions.width * 0.3, dimensions.height * 0.3, 0, dimensions.width * 0.3, dimensions.height * 0.3, dimensions.width * 0.8);\n      gradient1.addColorStop(0, 'rgba(5, 8, 12, 0.9)');\n      gradient1.addColorStop(0.3, 'rgba(10, 15, 20, 0.7)');\n      gradient1.addColorStop(0.7, 'rgba(0, 0, 0, 0.95)');\n      gradient1.addColorStop(1, 'transparent');\n      ctx.fillStyle = gradient1;\n      ctx.fillRect(0, 0, dimensions.width, dimensions.height);\n\n      // Second gradient layer\n      const gradient2 = ctx.createRadialGradient(dimensions.width * 0.7 + Math.sin(time) * 50, dimensions.height * 0.7 + Math.cos(time * 0.8) * 30, 0, dimensions.width * 0.7, dimensions.height * 0.7, dimensions.width * 0.6);\n      gradient2.addColorStop(0, 'rgba(8, 12, 18, 0.8)');\n      gradient2.addColorStop(0.4, 'rgba(15, 20, 25, 0.6)');\n      gradient2.addColorStop(0.8, 'rgba(0, 0, 0, 0.9)');\n      gradient2.addColorStop(1, 'transparent');\n      ctx.globalCompositeOperation = 'multiply';\n      ctx.fillStyle = gradient2;\n      ctx.fillRect(0, 0, dimensions.width, dimensions.height);\n      ctx.globalCompositeOperation = 'source-over';\n\n      // Draw and update particles\n      particles.forEach((particle, index) => {\n        // Update particle\n        particle.x += particle.speedX + Math.sin(time + index * 0.1) * 0.1;\n        particle.y += particle.speedY + Math.cos(time + index * 0.1) * 0.1;\n        particle.life++;\n\n        // Wrap around screen\n        if (particle.x < 0) particle.x = dimensions.width;\n        if (particle.x > dimensions.width) particle.x = 0;\n        if (particle.y < 0) particle.y = dimensions.height;\n        if (particle.y > dimensions.height) particle.y = 0;\n\n        // Reset particle if life exceeded\n        if (particle.life > particle.maxLife) {\n          particle.x = Math.random() * dimensions.width;\n          particle.y = Math.random() * dimensions.height;\n          particle.life = 0;\n          particle.opacity = Math.random() * 0.3 + 0.1;\n        }\n\n        // Draw particle\n        const alpha = particle.opacity * (1 - particle.life / particle.maxLife);\n        ctx.fillStyle = `rgba(255, 255, 255, ${alpha * 0.1})`;\n        ctx.beginPath();\n        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);\n        ctx.fill();\n      });\n\n      // Add noise texture\n      const imageData = ctx.getImageData(0, 0, dimensions.width, dimensions.height);\n      const data = imageData.data;\n      for (let i = 0; i < data.length; i += 4) {\n        if (Math.random() < 0.02) {\n          const noise = Math.random() * 10;\n          data[i] += noise; // Red\n          data[i + 1] += noise; // Green\n          data[i + 2] += noise; // Blue\n        }\n      }\n      ctx.putImageData(imageData, 0, 0);\n      animationRef.current = requestAnimationFrame(animate);\n    };\n    animate();\n    return () => {\n      window.removeEventListener('resize', updateSize);\n      if (animationRef.current) {\n        cancelAnimationFrame(animationRef.current);\n      }\n    };\n  }, [dimensions.width, dimensions.height]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `dark-veil-container ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(\"canvas\", {\n      ref: canvasRef,\n      className: \"dark-veil-canvas\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dark-veil-overlay\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 160,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 155,\n    columnNumber: 5\n  }, this);\n};\n_s(DarkVeil, \"wmQfh6sk44RhTBcfaDTp0svxuQ8=\");\n_c = DarkVeil;\nexport default DarkVeil;\nvar _c;\n$RefreshReg$(_c, \"DarkVeil\");", "map": {"version": 3, "names": ["React", "useRef", "useEffect", "useState", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON>", "className", "_s", "canvasRef", "animationRef", "dimensions", "setDimensions", "width", "height", "canvas", "current", "ctx", "getContext", "updateSize", "rect", "getBoundingClientRect", "dpr", "window", "devicePixelRatio", "scale", "style", "addEventListener", "particles", "particleCount", "i", "push", "x", "Math", "random", "y", "size", "speedX", "speedY", "opacity", "life", "maxLife", "time", "animate", "fillStyle", "fillRect", "gradient1", "createRadialGradient", "addColorStop", "gradient2", "sin", "cos", "globalCompositeOperation", "for<PERSON>ach", "particle", "index", "alpha", "beginPath", "arc", "PI", "fill", "imageData", "getImageData", "data", "length", "noise", "putImageData", "requestAnimationFrame", "removeEventListener", "cancelAnimationFrame", "children", "ref", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/DarkVeil.js"], "sourcesContent": ["import React, { useRef, useEffect, useState } from 'react';\nimport './DarkVeil.css';\n\nconst DarkVeil = ({ className = \"\" }) => {\n  const canvasRef = useRef(null);\n  const animationRef = useRef(null);\n  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });\n\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n\n    const ctx = canvas.getContext('2d');\n    if (!ctx) return;\n\n    // Set canvas size\n    const updateSize = () => {\n      const rect = canvas.getBoundingClientRect();\n      const dpr = window.devicePixelRatio || 1;\n      \n      canvas.width = rect.width * dpr;\n      canvas.height = rect.height * dpr;\n      \n      ctx.scale(dpr, dpr);\n      canvas.style.width = rect.width + 'px';\n      canvas.style.height = rect.height + 'px';\n      \n      setDimensions({ width: rect.width, height: rect.height });\n    };\n\n    updateSize();\n    window.addEventListener('resize', updateSize);\n\n    // Veil particles\n    const particles = [];\n    const particleCount = 150;\n\n    // Initialize particles\n    for (let i = 0; i < particleCount; i++) {\n      particles.push({\n        x: Math.random() * dimensions.width,\n        y: Math.random() * dimensions.height,\n        size: Math.random() * 2 + 0.5,\n        speedX: (Math.random() - 0.5) * 0.5,\n        speedY: (Math.random() - 0.5) * 0.5,\n        opacity: Math.random() * 0.3 + 0.1,\n        life: Math.random() * 100,\n        maxLife: Math.random() * 100 + 50\n      });\n    }\n\n    // Animation loop\n    let time = 0;\n    const animate = () => {\n      time += 0.01;\n      \n      // Clear canvas with dark background\n      ctx.fillStyle = 'rgba(0, 0, 0, 0.02)';\n      ctx.fillRect(0, 0, dimensions.width, dimensions.height);\n\n      // Create veil effect with gradients\n      const gradient1 = ctx.createRadialGradient(\n        dimensions.width * 0.3, \n        dimensions.height * 0.3, \n        0,\n        dimensions.width * 0.3, \n        dimensions.height * 0.3, \n        dimensions.width * 0.8\n      );\n      gradient1.addColorStop(0, 'rgba(5, 8, 12, 0.9)');\n      gradient1.addColorStop(0.3, 'rgba(10, 15, 20, 0.7)');\n      gradient1.addColorStop(0.7, 'rgba(0, 0, 0, 0.95)');\n      gradient1.addColorStop(1, 'transparent');\n\n      ctx.fillStyle = gradient1;\n      ctx.fillRect(0, 0, dimensions.width, dimensions.height);\n\n      // Second gradient layer\n      const gradient2 = ctx.createRadialGradient(\n        dimensions.width * 0.7 + Math.sin(time) * 50, \n        dimensions.height * 0.7 + Math.cos(time * 0.8) * 30, \n        0,\n        dimensions.width * 0.7, \n        dimensions.height * 0.7, \n        dimensions.width * 0.6\n      );\n      gradient2.addColorStop(0, 'rgba(8, 12, 18, 0.8)');\n      gradient2.addColorStop(0.4, 'rgba(15, 20, 25, 0.6)');\n      gradient2.addColorStop(0.8, 'rgba(0, 0, 0, 0.9)');\n      gradient2.addColorStop(1, 'transparent');\n\n      ctx.globalCompositeOperation = 'multiply';\n      ctx.fillStyle = gradient2;\n      ctx.fillRect(0, 0, dimensions.width, dimensions.height);\n      ctx.globalCompositeOperation = 'source-over';\n\n      // Draw and update particles\n      particles.forEach((particle, index) => {\n        // Update particle\n        particle.x += particle.speedX + Math.sin(time + index * 0.1) * 0.1;\n        particle.y += particle.speedY + Math.cos(time + index * 0.1) * 0.1;\n        particle.life++;\n\n        // Wrap around screen\n        if (particle.x < 0) particle.x = dimensions.width;\n        if (particle.x > dimensions.width) particle.x = 0;\n        if (particle.y < 0) particle.y = dimensions.height;\n        if (particle.y > dimensions.height) particle.y = 0;\n\n        // Reset particle if life exceeded\n        if (particle.life > particle.maxLife) {\n          particle.x = Math.random() * dimensions.width;\n          particle.y = Math.random() * dimensions.height;\n          particle.life = 0;\n          particle.opacity = Math.random() * 0.3 + 0.1;\n        }\n\n        // Draw particle\n        const alpha = particle.opacity * (1 - particle.life / particle.maxLife);\n        ctx.fillStyle = `rgba(255, 255, 255, ${alpha * 0.1})`;\n        ctx.beginPath();\n        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);\n        ctx.fill();\n      });\n\n      // Add noise texture\n      const imageData = ctx.getImageData(0, 0, dimensions.width, dimensions.height);\n      const data = imageData.data;\n      \n      for (let i = 0; i < data.length; i += 4) {\n        if (Math.random() < 0.02) {\n          const noise = Math.random() * 10;\n          data[i] += noise;     // Red\n          data[i + 1] += noise; // Green\n          data[i + 2] += noise; // Blue\n        }\n      }\n      \n      ctx.putImageData(imageData, 0, 0);\n\n      animationRef.current = requestAnimationFrame(animate);\n    };\n\n    animate();\n\n    return () => {\n      window.removeEventListener('resize', updateSize);\n      if (animationRef.current) {\n        cancelAnimationFrame(animationRef.current);\n      }\n    };\n  }, [dimensions.width, dimensions.height]);\n\n  return (\n    <div className={`dark-veil-container ${className}`}>\n      <canvas\n        ref={canvasRef}\n        className=\"dark-veil-canvas\"\n      />\n      <div className=\"dark-veil-overlay\" />\n    </div>\n  );\n};\n\nexport default DarkVeil;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,OAAO,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,QAAQ,GAAGA,CAAC;EAAEC,SAAS,GAAG;AAAG,CAAC,KAAK;EAAAC,EAAA;EACvC,MAAMC,SAAS,GAAGR,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMS,YAAY,GAAGT,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM,CAACU,UAAU,EAAEC,aAAa,CAAC,GAAGT,QAAQ,CAAC;IAAEU,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE,CAAC,CAAC;EAErEZ,SAAS,CAAC,MAAM;IACd,MAAMa,MAAM,GAAGN,SAAS,CAACO,OAAO;IAChC,IAAI,CAACD,MAAM,EAAE;IAEb,MAAME,GAAG,GAAGF,MAAM,CAACG,UAAU,CAAC,IAAI,CAAC;IACnC,IAAI,CAACD,GAAG,EAAE;;IAEV;IACA,MAAME,UAAU,GAAGA,CAAA,KAAM;MACvB,MAAMC,IAAI,GAAGL,MAAM,CAACM,qBAAqB,CAAC,CAAC;MAC3C,MAAMC,GAAG,GAAGC,MAAM,CAACC,gBAAgB,IAAI,CAAC;MAExCT,MAAM,CAACF,KAAK,GAAGO,IAAI,CAACP,KAAK,GAAGS,GAAG;MAC/BP,MAAM,CAACD,MAAM,GAAGM,IAAI,CAACN,MAAM,GAAGQ,GAAG;MAEjCL,GAAG,CAACQ,KAAK,CAACH,GAAG,EAAEA,GAAG,CAAC;MACnBP,MAAM,CAACW,KAAK,CAACb,KAAK,GAAGO,IAAI,CAACP,KAAK,GAAG,IAAI;MACtCE,MAAM,CAACW,KAAK,CAACZ,MAAM,GAAGM,IAAI,CAACN,MAAM,GAAG,IAAI;MAExCF,aAAa,CAAC;QAAEC,KAAK,EAAEO,IAAI,CAACP,KAAK;QAAEC,MAAM,EAAEM,IAAI,CAACN;MAAO,CAAC,CAAC;IAC3D,CAAC;IAEDK,UAAU,CAAC,CAAC;IACZI,MAAM,CAACI,gBAAgB,CAAC,QAAQ,EAAER,UAAU,CAAC;;IAE7C;IACA,MAAMS,SAAS,GAAG,EAAE;IACpB,MAAMC,aAAa,GAAG,GAAG;;IAEzB;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,aAAa,EAAEC,CAAC,EAAE,EAAE;MACtCF,SAAS,CAACG,IAAI,CAAC;QACbC,CAAC,EAAEC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAGvB,UAAU,CAACE,KAAK;QACnCsB,CAAC,EAAEF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAGvB,UAAU,CAACG,MAAM;QACpCsB,IAAI,EAAEH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG;QAC7BG,MAAM,EAAE,CAACJ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG;QACnCI,MAAM,EAAE,CAACL,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG;QACnCK,OAAO,EAAEN,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;QAClCM,IAAI,EAAEP,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;QACzBO,OAAO,EAAER,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;MACjC,CAAC,CAAC;IACJ;;IAEA;IACA,IAAIQ,IAAI,GAAG,CAAC;IACZ,MAAMC,OAAO,GAAGA,CAAA,KAAM;MACpBD,IAAI,IAAI,IAAI;;MAEZ;MACAzB,GAAG,CAAC2B,SAAS,GAAG,qBAAqB;MACrC3B,GAAG,CAAC4B,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAElC,UAAU,CAACE,KAAK,EAAEF,UAAU,CAACG,MAAM,CAAC;;MAEvD;MACA,MAAMgC,SAAS,GAAG7B,GAAG,CAAC8B,oBAAoB,CACxCpC,UAAU,CAACE,KAAK,GAAG,GAAG,EACtBF,UAAU,CAACG,MAAM,GAAG,GAAG,EACvB,CAAC,EACDH,UAAU,CAACE,KAAK,GAAG,GAAG,EACtBF,UAAU,CAACG,MAAM,GAAG,GAAG,EACvBH,UAAU,CAACE,KAAK,GAAG,GACrB,CAAC;MACDiC,SAAS,CAACE,YAAY,CAAC,CAAC,EAAE,qBAAqB,CAAC;MAChDF,SAAS,CAACE,YAAY,CAAC,GAAG,EAAE,uBAAuB,CAAC;MACpDF,SAAS,CAACE,YAAY,CAAC,GAAG,EAAE,qBAAqB,CAAC;MAClDF,SAAS,CAACE,YAAY,CAAC,CAAC,EAAE,aAAa,CAAC;MAExC/B,GAAG,CAAC2B,SAAS,GAAGE,SAAS;MACzB7B,GAAG,CAAC4B,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAElC,UAAU,CAACE,KAAK,EAAEF,UAAU,CAACG,MAAM,CAAC;;MAEvD;MACA,MAAMmC,SAAS,GAAGhC,GAAG,CAAC8B,oBAAoB,CACxCpC,UAAU,CAACE,KAAK,GAAG,GAAG,GAAGoB,IAAI,CAACiB,GAAG,CAACR,IAAI,CAAC,GAAG,EAAE,EAC5C/B,UAAU,CAACG,MAAM,GAAG,GAAG,GAAGmB,IAAI,CAACkB,GAAG,CAACT,IAAI,GAAG,GAAG,CAAC,GAAG,EAAE,EACnD,CAAC,EACD/B,UAAU,CAACE,KAAK,GAAG,GAAG,EACtBF,UAAU,CAACG,MAAM,GAAG,GAAG,EACvBH,UAAU,CAACE,KAAK,GAAG,GACrB,CAAC;MACDoC,SAAS,CAACD,YAAY,CAAC,CAAC,EAAE,sBAAsB,CAAC;MACjDC,SAAS,CAACD,YAAY,CAAC,GAAG,EAAE,uBAAuB,CAAC;MACpDC,SAAS,CAACD,YAAY,CAAC,GAAG,EAAE,oBAAoB,CAAC;MACjDC,SAAS,CAACD,YAAY,CAAC,CAAC,EAAE,aAAa,CAAC;MAExC/B,GAAG,CAACmC,wBAAwB,GAAG,UAAU;MACzCnC,GAAG,CAAC2B,SAAS,GAAGK,SAAS;MACzBhC,GAAG,CAAC4B,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAElC,UAAU,CAACE,KAAK,EAAEF,UAAU,CAACG,MAAM,CAAC;MACvDG,GAAG,CAACmC,wBAAwB,GAAG,aAAa;;MAE5C;MACAxB,SAAS,CAACyB,OAAO,CAAC,CAACC,QAAQ,EAAEC,KAAK,KAAK;QACrC;QACAD,QAAQ,CAACtB,CAAC,IAAIsB,QAAQ,CAACjB,MAAM,GAAGJ,IAAI,CAACiB,GAAG,CAACR,IAAI,GAAGa,KAAK,GAAG,GAAG,CAAC,GAAG,GAAG;QAClED,QAAQ,CAACnB,CAAC,IAAImB,QAAQ,CAAChB,MAAM,GAAGL,IAAI,CAACkB,GAAG,CAACT,IAAI,GAAGa,KAAK,GAAG,GAAG,CAAC,GAAG,GAAG;QAClED,QAAQ,CAACd,IAAI,EAAE;;QAEf;QACA,IAAIc,QAAQ,CAACtB,CAAC,GAAG,CAAC,EAAEsB,QAAQ,CAACtB,CAAC,GAAGrB,UAAU,CAACE,KAAK;QACjD,IAAIyC,QAAQ,CAACtB,CAAC,GAAGrB,UAAU,CAACE,KAAK,EAAEyC,QAAQ,CAACtB,CAAC,GAAG,CAAC;QACjD,IAAIsB,QAAQ,CAACnB,CAAC,GAAG,CAAC,EAAEmB,QAAQ,CAACnB,CAAC,GAAGxB,UAAU,CAACG,MAAM;QAClD,IAAIwC,QAAQ,CAACnB,CAAC,GAAGxB,UAAU,CAACG,MAAM,EAAEwC,QAAQ,CAACnB,CAAC,GAAG,CAAC;;QAElD;QACA,IAAImB,QAAQ,CAACd,IAAI,GAAGc,QAAQ,CAACb,OAAO,EAAE;UACpCa,QAAQ,CAACtB,CAAC,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAGvB,UAAU,CAACE,KAAK;UAC7CyC,QAAQ,CAACnB,CAAC,GAAGF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAGvB,UAAU,CAACG,MAAM;UAC9CwC,QAAQ,CAACd,IAAI,GAAG,CAAC;UACjBc,QAAQ,CAACf,OAAO,GAAGN,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;QAC9C;;QAEA;QACA,MAAMsB,KAAK,GAAGF,QAAQ,CAACf,OAAO,IAAI,CAAC,GAAGe,QAAQ,CAACd,IAAI,GAAGc,QAAQ,CAACb,OAAO,CAAC;QACvExB,GAAG,CAAC2B,SAAS,GAAG,uBAAuBY,KAAK,GAAG,GAAG,GAAG;QACrDvC,GAAG,CAACwC,SAAS,CAAC,CAAC;QACfxC,GAAG,CAACyC,GAAG,CAACJ,QAAQ,CAACtB,CAAC,EAAEsB,QAAQ,CAACnB,CAAC,EAAEmB,QAAQ,CAAClB,IAAI,EAAE,CAAC,EAAEH,IAAI,CAAC0B,EAAE,GAAG,CAAC,CAAC;QAC9D1C,GAAG,CAAC2C,IAAI,CAAC,CAAC;MACZ,CAAC,CAAC;;MAEF;MACA,MAAMC,SAAS,GAAG5C,GAAG,CAAC6C,YAAY,CAAC,CAAC,EAAE,CAAC,EAAEnD,UAAU,CAACE,KAAK,EAAEF,UAAU,CAACG,MAAM,CAAC;MAC7E,MAAMiD,IAAI,GAAGF,SAAS,CAACE,IAAI;MAE3B,KAAK,IAAIjC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiC,IAAI,CAACC,MAAM,EAAElC,CAAC,IAAI,CAAC,EAAE;QACvC,IAAIG,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI,EAAE;UACxB,MAAM+B,KAAK,GAAGhC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE;UAChC6B,IAAI,CAACjC,CAAC,CAAC,IAAImC,KAAK,CAAC,CAAK;UACtBF,IAAI,CAACjC,CAAC,GAAG,CAAC,CAAC,IAAImC,KAAK,CAAC,CAAC;UACtBF,IAAI,CAACjC,CAAC,GAAG,CAAC,CAAC,IAAImC,KAAK,CAAC,CAAC;QACxB;MACF;MAEAhD,GAAG,CAACiD,YAAY,CAACL,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;MAEjCnD,YAAY,CAACM,OAAO,GAAGmD,qBAAqB,CAACxB,OAAO,CAAC;IACvD,CAAC;IAEDA,OAAO,CAAC,CAAC;IAET,OAAO,MAAM;MACXpB,MAAM,CAAC6C,mBAAmB,CAAC,QAAQ,EAAEjD,UAAU,CAAC;MAChD,IAAIT,YAAY,CAACM,OAAO,EAAE;QACxBqD,oBAAoB,CAAC3D,YAAY,CAACM,OAAO,CAAC;MAC5C;IACF,CAAC;EACH,CAAC,EAAE,CAACL,UAAU,CAACE,KAAK,EAAEF,UAAU,CAACG,MAAM,CAAC,CAAC;EAEzC,oBACET,OAAA;IAAKE,SAAS,EAAE,uBAAuBA,SAAS,EAAG;IAAA+D,QAAA,gBACjDjE,OAAA;MACEkE,GAAG,EAAE9D,SAAU;MACfF,SAAS,EAAC;IAAkB;MAAAiE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CAAC,eACFtE,OAAA;MAAKE,SAAS,EAAC;IAAmB;MAAAiE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAClC,CAAC;AAEV,CAAC;AAACnE,EAAA,CA/JIF,QAAQ;AAAAsE,EAAA,GAARtE,QAAQ;AAiKd,eAAeA,QAAQ;AAAC,IAAAsE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}