{"version": 3, "names": ["_toPrimitive", "require", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arg", "key", "toPrimitive", "String"], "sources": ["../../src/helpers/toPropertyKey.ts"], "sourcesContent": ["/* @minVersion 7.1.5 */\n\n// https://tc39.es/ecma262/#sec-topropertykey\n\nimport toPrimitive from \"./toPrimitive.ts\";\n\nexport default function toPropertyKey(arg: unknown) {\n  var key = toPrimitive(arg, \"string\");\n  return typeof key === \"symbol\" ? key : String(key);\n}\n"], "mappings": ";;;;;;AAIA,IAAAA,YAAA,GAAAC,OAAA;AAEe,SAASC,aAAaA,CAACC,GAAY,EAAE;EAClD,IAAIC,GAAG,GAAG,IAAAC,oBAAW,EAACF,GAAG,EAAE,QAAQ,CAAC;EACpC,OAAO,OAAOC,GAAG,KAAK,QAAQ,GAAGA,GAAG,GAAGE,MAAM,CAACF,GAAG,CAAC;AACpD", "ignoreList": []}